ARG ENV
ARG ECR_URI
FROM ${ECR_URI}/curl:8.8.0 AS GUANCE_AGENT
ARG GUANCE_AGENT_URL="https://static.guance.com/dd-image/dd-java-agent.jar"
RUN curl --silent --fail -L ${GUANCE_AGENT_URL} -o "/tmp/dd-java-agent.jar"

FROM ${ECR_URI}/gradle:8.5.0 AS build
ARG SONAR_TOKEN
ARG SONAR_HOST
ARG SONAR_PROJECT
ARG SONAR_ORGANIZATION
ARG SONAR_BRANCH
WORKDIR /app
COPY . .

RUN gradle clean build jacocoTestReport sonar -x spotlessCheck -x spotlessApply -Dsonar.login=$SONAR_TOKEN -Dsonar.host.url=$SONAR_HOST -Dsonar.projectKey=$SONAR_PROJECT -Dsonar.organization=$SONAR_ORGANIZATION -Dsonar.branch.name=$SONAR_BRANCH -Dorg.gradle.jvmargs="-XX:MetaspaceSize=1024M -XX:MaxMetaspaceSize=1024M"

FROM ${ECR_URI}/amazoncorretto-jdk:17-alpine3.18-jdk AS deploy
ARG profile
WORKDIR /app
COPY --from=build /app/build/libs/reconciliation-service.jar /app/service.jar
COPY --from=build /app/src/main/resources/* ./
COPY --from=GUANCE_AGENT /tmp/dd-java-agent.jar ./dd-java-agent.jar

ENV JAVA_OBSERVATION="-Dotel.service.name=cashless-cabcharge-gateway-reconciliation-svc"

ENV JAVA_OPTS="-Dspring.profiles.active=$profile"
EXPOSE 8080
ENTRYPOINT java -javaagent:./dd-java-agent.jar $JAVA_OBSERVATION $JAVA_OPTS -jar /app/service.jar
