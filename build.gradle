plugins {
	id 'org.springframework.boot' version '3.1.0'
	id 'io.spring.dependency-management' version '1.1.0'
	id 'java'
	id 'org.sonarqube' version "4.0.0.2929"
    id 'com.diffplug.spotless' version '6.23.0'
	id 'jacoco'
}

group = 'com.cdg.zigpro.cabcharge.reconciliation'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

bootJar {
    archiveFileName = 'reconciliation-service.jar'
}
repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation('org.springframework.boot:spring-boot-starter-data-jpa') {
			exclude group: 'org.hibernate'
	}    
	implementation 'org.postgresql:postgresql:42.7.5'

	implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:3.0.2'

	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter-test:3.0.2'
	implementation('org.jpos:jpos:2.1.+') {
		exclude(module: 'junit')
		exclude(module: 'hamcrest-core')
	}
	implementation 'org.springframework.boot:spring-boot-starter-quartz'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.1.0")

    // Configuration AWS Secret Manager
    implementation("io.awspring.cloud:spring-cloud-aws-starter:3.0.0")
    implementation("io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:3.0.0")

    testRuntimeOnly 'com.h2database:h2'
    // Test implementation
    testImplementation(platform("org.junit:junit-bom:5.9.2"))
    testImplementation("org.junit.jupiter:junit-jupiter")
    testImplementation("org.mockito:mockito-junit-jupiter:4.5.1")
}

sonar {
    def coverageExcludeDirList = [
            // Application
            "**/dtos/**",
            "**/models/**"
    ]
    def coverageExcludeDir = coverageExcludeDirList.join(",")

    def duplicationExcludeDirList = [
            // Application
            "**/dtos/**",
            "**/models/**"
    ]
    def duplicationExcludeDir = duplicationExcludeDirList.join(",")

    properties {
        property "sonar.projectKey", "cdgtaxi_cabcharge-gateway-reconciliation"
        property "sonar.projectName", "cdgtaxi_cabcharge-gateway-reconciliation"
        property "sonar.java.binaries", "./build/classes"
        property "sonar.java.sources", "./src"
        property "sonar.tests", "./src/test"
        property "sonar.dynamicAnalysis", "reuseReports"
        property "sonar.junit.reportsPath", "./build/test-results/test/TEST-*.xml"
        property "sonar.coverage.exclusions", coverageExcludeDir
        property "sonar.cpd.exclusions", duplicationExcludeDir
        property "sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/testCodeCoverageReport/testCodeCoverageReport.xml"
    }
}


spotless {
    kotlinGradle {
        targetExclude("**/*.gradle")
    }

    java {
        targetExclude("**/generated/**/*.java")
        removeUnusedImports()
        importOrder()
        googleJavaFormat()
        endWithNewline()
        indentWithSpaces(2)
        replace("SonarQube Comments", "// NOSONAR", "//NOSONAR")
    }
}
tasks.build { dependsOn("spotlessApply") }

test {
    useJUnitPlatform()
	ignoreFailures = true //build success even when test failure occurs
	finalizedBy jacocoTestReport // report is always generated after tests run
}

jacocoTestReport {
	dependsOn test // tests are required to run before generating the report
    reports {
        xml.required = true
        csv.required = true
        xml.destination file("$buildDir/reports/jacoco/testCodeCoverageReport/testCodeCoverageReport.xml")
        csv.destination file("$buildDir/reports/jacoco/testCodeCoverageReport/testCodeCoverageReport.csv")
    }
}