import os
import json
import boto3
import requests
from botocore.exceptions import ClientError
eventbridge_client = boto3.client('events', region_name=os.environ['AWS_REGION'])
def process_event(event, context):
    # Make an HTTP POST request with event URL and headers
    try:
        response = send_request(event)
    except Exception as e:
        print('Exception is :', e)
        raise Exception(f'Failed to make HTTP request to application. Error: {e}')
    if response.status_code != 200:
        raise Exception(f'Application unable to process event. Error: {response_body}')
    # If the HTTP call was successful and the inbound event had a 'return-response-event' flag,
    # put a response event on the EventBridge bus
    if response.status_code == 200:
        print('Response status 200 ok')
    return {
        'statusCode': 200
    }
def send_request(event):
    response = requests.post(os.environ['CABCHARGE_GATEWAY_RECON_API_URL'], headers={})
    return response
