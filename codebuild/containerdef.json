{"essential": true, "image": "<IMAGE1_NAME>", "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": ""}}, "name": "{NAMESPACE}-{APPNAME}-{ENV}", "portMappings": [{"containerPort": "{CONTAINER_PORT}", "hostPort": "{CONTAINER_PORT}", "protocol": "tcp"}], "environment": [{"name": "OTEL_EXPORTER_OTLP_ENDPOINT", "value": "http://localhost:4317"}, {"name": "OTEL_RESOURCE_ATTRIBUTES", "value": "service.namespace={NAMESPACE},service.name={APPNAME},environment={ENV}"}], "cpu": "{APP_CPU}", "memory": "{APP_MEMORY}"}