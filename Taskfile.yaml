version: 3

includes:
  common:
    taskfile: /taskfiles # This is common taskfile that is included in docker image
    optional: true

env:
  SHA: '{{ .SHA }}' # Commit hash that triggered pipeline
  WORKSPACE: '{{ .WORKSPACE }}' # It will be injected by CI
  REPO_REVISION: '{{ .REPO_REVISION }}' # Branch or tag that triggered pipeline
  APP_NAME: 'cabcharge-gateway-reconciliation'
  HELM_CHART: *****************:cdgtaxi/cabcharge-k8s.git
  VALUE_FILE: cabcharge-gateway-reconciliation-values.yaml
  AWS_DEFAULT_REGION: 'ap-southeast-1'
  DOCKERFILE: 'Dockerfile'
  SONAR_HOST: https://sonarcloud.io
  SONAR_PROJECT:  cdgtaxi_cabcharge-gateway-reconciliation
  SONAR_ORGANIZATION: cdg-zig
  SONAR_BRANCH: '{{ .REPO_REVISION }}'
dotenv:
  - .env

vars:
  ENVIRONMENT:
    sh: echo $([[ {{ .REPO_REVISION }} == "sit" ]] && echo "sit" || ([[ {{ .REPO_REVISION }} == "uat" || {{ .REPO_REVISION }} == "uat1" ]] && echo "uat1") || ([[ {{ .REPO_REVISION }} == "master" ]] && echo "prod") || echo "sit")
  WORKLOAD_ACCOUNT_ID:
    sh: echo $([[ "{{ .REPO_REVISION }}" == "master" ]] && echo "************" || echo "************")
  IMAGE_REGISTRY:
    sh: echo '{{.WORKLOAD_ACCOUNT_ID }}.dkr.ecr.{{ .AWS_DEFAULT_REGION }}.amazonaws.com'
  IMAGE_NAME: 
    sh: echo '{{ .IMAGE_REGISTRY }}/{{ .APP_NAME }}'
  IMAGE_NAME_FULL: 
    sh: echo '{{ .IMAGE_NAME }}:{{ substr 0 8 .SHA }}'
  secrets-manager:
    map:
      {
        SONAR_TOKEN: 'arn:aws:secretsmanager:ap-southeast-1:************:secret:ngp-terraform/sonarcloud-8iGTfy:SONAR_TOKEN'
      }

tasks:
  init:
    cmds:
      - touch .env
      - echo "Start integrating secret"
      - for: {var: secrets-manager}
        cmd: task common:aws:parse-secrets-manager SECRET_KEY={{.KEY}} SECRET_SPEC={{.ITEM}}
      - for: {var: parameter-store}
        cmd: task common:aws:parse-paramater-store PARAM_KEY={{.KEY}} PARAM_SPEC={{.ITEM}}
  install:
    cmds:
      - echo "Install"
    internal: true
  pre_build:
    cmds:
      - task: install
    internal: true
  post_build:
    cmds:
      - echo -n {{ .IMAGE_NAME_FULL }} > {{ .IMAGE_RESULT }}
      - echo -n {{ .HELM_CHART }} > {{ .HELM_CHART_RESULT }}
      - echo -n {{ .VALUE_FILE }} > {{ .VALUE_FILE_RESULT }}
    internal: true
  build:
    cmds:
      - task: pre_build
      - defer:
          task: post_build
      - cmd: aws ecr get-login-password --region ${AWS_DEFAULT_REGION} | buildah login --username AWS --password-stdin {{ .IMAGE_REGISTRY }}
      # Dev to edit from here
      - task: 'common:buildah'
        vars:
          TAG: '{{ .IMAGE_NAME_FULL }}'
          DOCKERFILE: '{{ .DOCKERFILE }}'
          OTHER_OPTS: >-
            --build-arg ENV={{ .ENVIRONMENT }}
            --build-arg profile={{ .ENVIRONMENT }},json-logging
            --build-arg SONAR_TOKEN=$SONAR_TOKEN
            --build-arg SONAR_HOST=$SONAR_HOST
            --build-arg SONAR_PROJECT=$SONAR_PROJECT
            --build-arg SONAR_ORGANIZATION=$SONAR_ORGANIZATION
            --build-arg SONAR_BRANCH=$SONAR_BRANCH
            --build-arg ECR_URI={{ .IMAGE_REGISTRY }}
            --cache-from {{ .IMAGE_NAME_FULL }}
