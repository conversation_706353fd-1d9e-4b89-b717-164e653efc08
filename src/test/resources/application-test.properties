
# Database configuration
spring.datasource.url: jdbc:h2:mem:db;DB_CLOSE_DELAY=-1
spring.datasource.driverClassName: org.h2.Driver
spring.datasource.username: sa
spring.datasource.password: password
spring.h2.console.enabled=true

# MyBatis configuration
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.use-generated-keys=true

as.server.ip=***********
as.server.port=11111
as.iso.file=iso87binary-CREDIT.xml
settlement.output.file.path=./build
settlement.request.timeout.second=2
settlement.request.max.retry.attempt=2
management.endpoints.web.exposure.include=health,prometheus
