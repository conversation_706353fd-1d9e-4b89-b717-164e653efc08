package com.cdg.zigpro.reconciliation;

import com.cdg.zigpro.reconciliation.domain.models.ReconciliationJobParam;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTnxResult;
import com.cdg.zigpro.reconciliation.domain.models.entities.TransactionDetail;
import com.cdg.zigpro.reconciliation.domain.services.IReconciliationFileService;
import com.cdg.zigpro.reconciliation.domain.services.IReconciliationRepository;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(classes = ReconciliationApplication.class)
@TestPropertySource(locations = "classpath:application-test.properties")
class ReconciliationApplicationTests {

  @Autowired private IReconciliationFileService reconciliationFileService;

  @MockBean private IReconciliationRepository reconciliationRepository;

  @Value("${settlement.output.file.path}")
  private String outputDirectory;

  @Test
  public void testCreateSettlementFile() {
    var transactions = generateDummyTransactionDetails();
    var records = generateDummyRecords(transactions);
    // Generate the file using the reconciliationFileService
    reconciliationFileService.generateSettlementFile(
        generateDummyBatchNo(), records, transactions, null);
  }

  @Test
  public void testCreateSettlementFileWithParam() {
    var transactions = generateDummyTransactionDetails();
    var records = generateDummyRecords(transactions);
    // Generate the file using the reconciliationFileService
    reconciliationFileService.generateSettlementFile(
        generateDummyBatchNo(), records, transactions, new ReconciliationJobParam());
  }

  private String generateDummyBatchNo() {
    // Replace this with your actual implementation to generate a dummy batchNo
    return "BATCH001";
  }

  private HashMap<String, SettlementTnxResult> generateDummyRecords(
      List<TransactionDetail> transactions) {
    // Create a dummy HashMap to store the records (transactionId, status)
    HashMap<String, SettlementTnxResult> records = new HashMap<>();

    // Generate dummy records for each transaction
    for (TransactionDetail transaction : transactions) {
      String transactionId = transaction.getId().toString();
      // Set the initial status to false (or any default value)
      var tnxResult = new SettlementTnxResult();
      tnxResult.setMid(transaction.getMerchantId());
      tnxResult.setTid(transaction.getTerminalId());
      tnxResult.setSettled(true);
      records.put(transactionId, tnxResult);
    }

    return records;
  }

  public static List<TransactionDetail> generateDummyTransactionDetails() {
    List<TransactionDetail> transactionDetails = new ArrayList<>();

    transactionDetails.add(
        createDummyTransactionDetail(
            "1",
            BigDecimal.valueOf(100.0),
            true,
            "https://example.com/cancel",
            "John Doe",
            "1234567890123456",
            "abcdefg123456789",
            "USD",
            "Custom Data",
            "Transaction Description",
            "1223",
            "127.0.0.1",
            "MERCHANT123",
            "1234567890",
            "https://example.com/notify",
            "ORD123",
            "pares123",
            "PROFILE123",
            "REQ123",
            "2023-07-01 10:00:00",
            "00",
            "Success",
            "https://example.com/return",
            "signature123",
            "2023-07-01 10:00:00",
            "AUTH123",
            "SALE",
            "REF123",
            "123456"));
    transactionDetails.add(
        createDummyTransactionDetail(
            "2",
            BigDecimal.valueOf(100.0),
            true,
            "https://example.com/cancel",
            "John Doe",
            "1234567890123456",
            "abcdefg123456789",
            "USD",
            "Custom Data",
            "Transaction Description",
            "1223",
            "127.0.0.1",
            "MERCHANT123",
            "1234567890",
            "https://example.com/notify",
            "ORD123",
            "pares123",
            "PROFILE123",
            "REQ123",
            "2023-07-01 10:00:00",
            "00",
            "Success",
            "https://example.com/return",
            "signature123",
            "2023-07-01 10:00:00",
            "AUTH123",
            "SALE",
            "REF123",
            "123456"));

    transactionDetails.add(
        createDummyTransactionDetail(
            "3",
            BigDecimal.valueOf(300.0),
            true,
            "https://example.com/cancel3",
            "Bob Johnson",
            "5555666677778888",
            "qwertyuiopasdfgh",
            "GBP",
            "More Data",
            "More Description",
            "0412",
            "********",
            "MERCHANT789",
            "555566667777",
            "https://example.com/notify3",
            "ORD789",
            "pares789",
            "PROFILE789",
            "REQ789",
            "2023-07-03 12:00:00",
            "00",
            "Success",
            "https://example.com/return3",
            "signature789",
            "2023-07-03 12:00:00",
            "AUTH789",
            "SALE",
            "REF789",
            "987654"));
    transactionDetails.add(
        createDummyTransactionDetail(
            "4",
            BigDecimal.valueOf(200.0),
            true,
            "https://example.com/cancel",
            "John Doe",
            "1234567890123456",
            "abcdefg123456789",
            "USD",
            "Custom Data",
            "Transaction Description",
            "1223",
            "127.0.0.1",
            "MERCHANT123",
            "1234567890",
            "https://example.com/notify",
            "ORD123",
            "pares123",
            "PROFILE123",
            "REQ123",
            "2023-07-01 10:00:00",
            "00",
            "Success",
            "https://example.com/return",
            "signature123",
            "2023-07-01 10:00:00",
            "AUTH123",
            "SALE",
            "REF123",
            "123456"));
    transactionDetails.add(
        createDummyTransactionDetail(
            "5",
            BigDecimal.valueOf(4500.0),
            true,
            "https://example.com/cancel",
            "John Doe",
            "1234567890123456",
            "abcdefg123456789",
            "USD",
            "Custom Data",
            "Transaction Description",
            "1223",
            "127.0.0.1",
            "MERCHANT123",
            "1234567890",
            "https://example.com/notify",
            "ORD123",
            "pares123",
            "PROFILE123",
            "REQ123",
            "2023-07-01 10:00:00",
            "00",
            "Success",
            "https://example.com/return",
            "signature123",
            "2023-07-01 10:00:00",
            "AUTH123",
            "SALE",
            "REF123",
            "123456"));

    return transactionDetails;
  }

  private static TransactionDetail createDummyTransactionDetail(
      String transactionId,
      BigDecimal amount,
      boolean autoDetect,
      String cancelUrl,
      String cardHolderName,
      String cardNumber,
      String cardToken,
      String currency,
      String customData,
      String description,
      String expiryDate,
      String ipAddress,
      String merchantAccountId,
      String mobile,
      String notificationUrl,
      String orderNumber,
      String paReq,
      String profileId,
      String requestId,
      String requestTimestamp,
      String responseCode,
      String responseMessage,
      String returnUrl,
      String signature,
      String transactionDateTime,
      String authorizationCode,
      String transactionType,
      String retRefNo,
      String sysTraceNo) {
    TransactionDetail transactionDetail = new TransactionDetail();
    transactionDetail.setId(Long.valueOf(transactionId));
    transactionDetail.setRequestedAmount(amount);
    transactionDetail.setCardNumber(cardNumber);
    transactionDetail.setCardTokenId(cardToken);
    transactionDetail.setRequestedAmountCurrency(currency);
    transactionDetail.setExpiryDate(expiryDate);
    transactionDetail.setMerchantAccountId(merchantAccountId);
    transactionDetail.setRequestId(requestId);
    transactionDetail.setRequestTimeStamp(fromInstantString(requestTimestamp));
    transactionDetail.setResponseCode(responseCode);
    transactionDetail.setCompletionTimeStamp(Timestamp.valueOf(transactionDateTime));
    transactionDetail.setAuthorizationCode(authorizationCode);
    transactionDetail.setTransactionType(transactionType);
    transactionDetail.setRetRefNo(retRefNo);
    transactionDetail.setSysTraceNo(sysTraceNo);

    return transactionDetail;
  }

  private static Instant fromInstantString(String stringDate) {
    // String stringDate = "2023-07-01 10:00:00";
    String pattern = "yyyy-MM-dd HH:mm:ss";
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
    LocalDateTime localDateTime = LocalDateTime.parse(stringDate, dateTimeFormatter);
    ZoneId zoneId = ZoneId.of("Asia/Singapore");
    ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
    return zonedDateTime.toInstant();
  }
}
