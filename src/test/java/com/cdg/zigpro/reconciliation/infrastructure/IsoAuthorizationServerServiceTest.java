package com.cdg.zigpro.reconciliation.infrastructure;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.cdg.zigpro.reconciliation.domain.constants.IsoConstants;
import com.cdg.zigpro.reconciliation.domain.models.BatchUploadRequest;
import com.cdg.zigpro.reconciliation.domain.models.SettlementRequest;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTrailerRequest;
import com.cdg.zigpro.reconciliation.infrastructure.configuration.AuthorizationServerChannelFactory;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.concurrent.ThreadLocalRandom;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ClassPathResource;

@ExtendWith(MockitoExtension.class)
public class IsoAuthorizationServerServiceTest {

  @InjectMocks private IsoAuthorizationServerService isoAuthorizationServerService;
  @Mock private AuthorizationServerChannelFactory channelFactory;

  @Test
  void testProcessSettlement() throws ISOException, IOException {
    when(channelFactory.sendIsoMsg(any())).thenReturn(initSettlementResponse());
    isoAuthorizationServerService.processSettlement(initSettlementRequest());
  }

  @Test
  void testProcessSettlementISOException() throws ISOException, IOException {
    when(channelFactory.sendIsoMsg(any())).thenThrow(new ISOException("exception"));
    isoAuthorizationServerService.processSettlement(initSettlementRequest());
  }

  @Test
  void testProcessSettlementIOException() throws ISOException, IOException {
    when(channelFactory.sendIsoMsg(any())).thenThrow(new IOException("exception"));
    isoAuthorizationServerService.processSettlement(initSettlementRequest());
  }

  private ISOMsg initSettlementResponse() {
    ISOMsg isoMsg = new ISOMsg();
    try {
      isoMsg.setMTI(IsoConstants.MTID_SETTLEMENT_RES);
    } catch (ISOException e) {
      throw new RuntimeException(e);
    }
    isoMsg.set(11, String.valueOf(ThreadLocalRandom.current().nextInt(100000, 999999)));
    isoMsg.set(12, "102601");
    isoMsg.set(13, "1114");
    isoMsg.set(37, "231114061056");
    isoMsg.set(38, "128727");
    isoMsg.set(39, "00");
    isoMsg.set(41, "90909090");
    return isoMsg;
  }

  @Test
  void testUploadBatch() throws ISOException, IOException {
    when(channelFactory.sendIsoMsg(any())).thenReturn(initSettlementResponse());
    isoAuthorizationServerService.uploadBatch(initBatchUploadRequest());
  }

  @Test
  void testUploadBatchISOException() throws ISOException, IOException {
    when(channelFactory.sendIsoMsg(any())).thenThrow(new ISOException("exception"));
    isoAuthorizationServerService.uploadBatch(initBatchUploadRequest());
  }

  @Test
  void testUploadBatchIOException() throws ISOException, IOException {
    when(channelFactory.sendIsoMsg(any())).thenThrow(new IOException("exception"));
    isoAuthorizationServerService.uploadBatch(initBatchUploadRequest());
  }

  @Test
  void testProcessSettlementTrailer() throws ISOException, IOException {
    when(channelFactory.sendIsoMsg(any())).thenReturn(initSettlementResponse());
    isoAuthorizationServerService.processSettlementTrailer(initSettlementTrailerRequest());
  }

  @Test
  void testProcessSettlementTrailerISOException() throws ISOException, IOException {
    when(channelFactory.sendIsoMsg(any())).thenThrow(new ISOException("exception"));
    isoAuthorizationServerService.processSettlementTrailer(initSettlementTrailerRequest());
  }

  @Test
  void testProcessSettlementTrailerIOException() throws ISOException, IOException {
    when(channelFactory.sendIsoMsg(any())).thenThrow(new IOException("exception"));
    isoAuthorizationServerService.processSettlementTrailer(initSettlementTrailerRequest());
  }

  private SettlementTrailerRequest initSettlementTrailerRequest() {
    SettlementTrailerRequest request = new SettlementTrailerRequest();
    request.setTraceNo("12345678");
    request.setMid("909090");
    request.setTid("909090");
    request.setBatchNo("BAT001");
    request.setSalesTotalCount(10);
    request.setSalesTotalAmount(new BigDecimal(1));
    return request;
  }

  private BatchUploadRequest initBatchUploadRequest() {
    BatchUploadRequest request = new BatchUploadRequest();
    request.setCardNo("123456789012");
    request.setAmount(new BigDecimal(1));
    request.setTraceNo("123456");
    request.setExpiryDate("1212");
    request.setPosConditionalMode("00");
    request.setRetrievalRefNo("123456");
    request.setResponseCode("00");
    request.setMid("909090");
    request.setTid("909090");
    request.setTraceNo("123456");
    return request;
  }

  private SettlementRequest initSettlementRequest() {
    SettlementRequest request = new SettlementRequest();
    request.setTraceNo("123456");
    request.setMid("909090");
    request.setTid("909090");
    request.setBatchNo("BAT1234");
    request.setSalesTotalCount(10);
    request.setSalesTotalAmount(new BigDecimal(100));
    return request;
  }

  private class MyPackager extends org.jpos.iso.packager.GenericPackager {

    public MyPackager() throws ISOException, IOException {
      super(new ClassPathResource("iso87binary-CREDIT.xml").getInputStream());
    }
  }
}
