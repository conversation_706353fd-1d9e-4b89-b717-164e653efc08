package com.cdg.zigpro.reconciliation.infrastructure.db;

import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import java.util.ArrayList;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PostgreSQLReconciliationRepositoryTest {

  @InjectMocks private PostgreSQLReconciliationRepository postgreSQLReconciliationRepository;
  @Mock private ReconciliationMapper reconciliationMapper;

  @Test
  void testInsertBatchProcess() {
    postgreSQLReconciliationRepository.insertBatchProcess(new BatchProcess());
  }

  @Test
  void testInsertBatchTransactions() {
    postgreSQLReconciliationRepository.insertBatchTransactions(new ArrayList<>());
  }

  @Test
  void testUpdateBatchProcess() {
    postgreSQLReconciliationRepository.updateBatchProcess(new BatchProcess());
  }
}
