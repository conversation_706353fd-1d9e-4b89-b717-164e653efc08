package com.cdg.zigpro.reconciliation.infrastructure.configuration;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.channel.PostChannel;
import org.jpos.iso.packager.GenericPackager;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.ReflectionUtils;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AuthorizationServerChannelFactoryTest {

  @InjectMocks private AuthorizationServerChannelFactory authorizationServerChannelFactory;

  @Test
  void testInit() throws IllegalAccessException {
    Field serverIp =
        ReflectionUtils.findFields(
                AuthorizationServerChannelFactory.class,
                f -> f.getName().equals("SERVER_IP"),
                ReflectionUtils.HierarchyTraversalMode.TOP_DOWN)
            .get(0);
    serverIp.setAccessible(true);
    serverIp.set(authorizationServerChannelFactory, "127.0.0.1");

    Field serverPort =
        ReflectionUtils.findFields(
                AuthorizationServerChannelFactory.class,
                f -> f.getName().equals("SERVER_PORT"),
                ReflectionUtils.HierarchyTraversalMode.TOP_DOWN)
            .get(0);
    serverPort.setAccessible(true);
    serverPort.set(authorizationServerChannelFactory, 11111);

    Assertions.assertThrows(
        IOException.class,
        () -> {
          authorizationServerChannelFactory.init();
        });
  }

  @Test
  void testGetChannel() {
    Assertions.assertThrows(
        RuntimeException.class,
        () -> {
          authorizationServerChannelFactory.getChannel();
        });
  }

  @Test
  void testSendIsoMsg() throws IllegalAccessException, ISOException {
    Field channels =
        ReflectionUtils.findFields(
                AuthorizationServerChannelFactory.class,
                f -> f.getName().equals("channels"),
                ReflectionUtils.HierarchyTraversalMode.TOP_DOWN)
            .get(0);
    channels.setAccessible(true);
    List<PostChannel> channelList = new ArrayList<>();
    channelList.add(new PostChannel("127.0.0.1", 11111, new GenericPackager()));
    channels.set(authorizationServerChannelFactory, channelList);

    Assertions.assertThrows(
        IOException.class,
        () -> {
          authorizationServerChannelFactory.sendIsoMsg(initIncomingMessage());
        });
  }

  @Test
  void testDisconnect() {
    boolean result = authorizationServerChannelFactory.disconnectChannels();
    Assertions.assertTrue(result);
  }

  private ISOMsg initIncomingMessage() {
    ISOMsg isoMsg = new ISOMsg();
    try {
      isoMsg.setMTI("0100");
    } catch (ISOException e) {
      throw new RuntimeException(e);
    }
    isoMsg.set(2, "6010896521001160");
    isoMsg.set(3, "000000");
    isoMsg.set(4, "000000000121");
    isoMsg.set(11, String.valueOf(ThreadLocalRandom.current().nextInt(100000, 999999)));
    isoMsg.set(14, "2912");
    isoMsg.set(22, "012");
    isoMsg.set(24, "012");
    isoMsg.set(25, "00");
    isoMsg.set(41, "90909090");
    isoMsg.set(42, "90909090");
    isoMsg.set(
        48,
        "000000000000000000000000000000002306121045372306121045421.3599370000000103.837400000001.3599400000000103.83740000000229VMF00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000");
    return isoMsg;
  }
}
