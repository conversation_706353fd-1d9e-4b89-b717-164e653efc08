package com.cdg.zigpro.reconciliation.application;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.cdg.zigpro.reconciliation.domain.models.ReconciliationJobParam;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import com.cdg.zigpro.reconciliation.domain.services.PaymentPortalService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

@WebMvcTest(PaymentPortalController.class)
public class PaymentPortalControllerTest {

  @Autowired private MockMvc mockMvc;

  @MockBean private PaymentPortalService paymentPortalService;

  @Value("${settlement.output.file.path}")
  private String SETTLEMENT_FILE_PATH;

  @Test
  void testExecuteSettlementProcess() throws Exception {
    ReconciliationJobParam jobParam = new ReconciliationJobParam();

    // You can mock the service if needed
    // when(paymentPortalService.executeSettlementProcess(any())).thenReturn(...);

    mockMvc
        .perform(post("/v1.0/settlements/execute").contentType("application/json").content("{}"))
        .andExpect(status().isOk());
  }

  @Test
  void testGetCabchargeSettlementFile_FileNotFound() throws Exception {
    String batchNo = "batch123";
    String filename = "non_existent.txt";

    BatchProcess batchProcess = new BatchProcess();
    batchProcess.setSettlementFile(filename);

    when(paymentPortalService.getBatchProcessByBatchNo(batchNo)).thenReturn(batchProcess);

    mockMvc.perform(get("/v1.0/settlements/download/" + batchNo)).andExpect(status().isNotFound());
  }

  @Test
  void testGetCabchargeSettlementFile_FileNameBlank() throws Exception {
    String batchNo = "batch123";

    BatchProcess batchProcess = new BatchProcess();
    batchProcess.setSettlementFile(""); // Filename is blank

    when(paymentPortalService.getBatchProcessByBatchNo(batchNo)).thenReturn(batchProcess);

    mockMvc.perform(get("/v1.0/settlements/download/" + batchNo)).andExpect(status().isNotFound());
  }
}
