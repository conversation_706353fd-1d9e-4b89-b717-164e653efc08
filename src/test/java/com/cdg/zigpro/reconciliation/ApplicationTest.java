package com.cdg.zigpro.reconciliation;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(useMainMethod = SpringBootTest.UseMainMethod.ALWAYS)
@TestPropertySource(locations = "classpath:application-test.properties")
public class ApplicationTest {

  @Test
  public void whenSpringContextIsBootstrapped_thenNoExceptions() {}
}
