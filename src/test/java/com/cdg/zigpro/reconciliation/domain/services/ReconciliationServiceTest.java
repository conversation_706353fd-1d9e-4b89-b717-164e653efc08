package com.cdg.zigpro.reconciliation.domain.services;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.cdg.zigpro.reconciliation.domain.models.BatchUploadResponse;
import com.cdg.zigpro.reconciliation.domain.models.SettlementResponse;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTrailerResponse;
import com.cdg.zigpro.reconciliation.domain.models.entities.TransactionDetail;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.platform.commons.util.ReflectionUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

@ExtendWith(MockitoExtension.class)
public class ReconciliationServiceTest {

  @InjectMocks private ReconciliationService reconciliationService;
  @Mock private ITransactionRepository transactionRepository;
  @Mock private IAuthorizationServerService authorizationServerService;
  @Mock private IReconciliationRepository reconciliationRepository;
  @Mock private IReconciliationFileService reconciliationFileService;
  private static final long TIMEOUT_MS = 3000;

  @BeforeEach
  void setup() throws IllegalAccessException {
    Field field =
        ReflectionUtils.findFields(
                ReconciliationService.class,
                f -> f.getName().equals("MAX_RETRY_ATTEMPTS"),
                ReflectionUtils.HierarchyTraversalMode.TOP_DOWN)
            .get(0);
    field.setAccessible(true);
    field.set(reconciliationService, 1);

    Field field2 =
        ReflectionUtils.findFields(
                ReconciliationService.class,
                f -> f.getName().equals("TIMEOUT_SECONDS"),
                ReflectionUtils.HierarchyTraversalMode.TOP_DOWN)
            .get(0);
    field2.setAccessible(true);
    field2.set(reconciliationService, 2);
  }

  @Test
  public void testProcessEmptyTransactionList() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions()).thenReturn(new ArrayList<>());
    reconciliationService.process(null);
  }

  @Test
  public void testProcessThrowException() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenThrow(new RuntimeException("Error in getting transaction list"));
    reconciliationService.process(null);
  }

  @Test
  public void testProcessTransactionList00() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenReturn(generateDummyTransactionDetails());
    when(authorizationServerService.processSettlement(any()))
        .thenReturn(setupSettlementResponse00());
    reconciliationService.process(null);
  }

  @Test
  public void testProcessTransactionList96() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenReturn(generateDummyTransactionDetails());
    when(authorizationServerService.processSettlement(any()))
        .thenReturn(setupSettlementResponse96());
    reconciliationService.process(null);
  }

  @Test
  public void testProcessTransactionList95() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenReturn(generateDummyTransactionDetails());
    when(authorizationServerService.processSettlement(any()))
        .thenReturn(setupSettlementResponse95());
    reconciliationService.process(null);
  }

  @Test
  public void testProcessTransactionListThrowTimeout() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenReturn(generateDummyTransactionDetails());
    when(authorizationServerService.processSettlement(any()))
        .thenAnswer(
            new Answer<String>() {
              @Override
              public String answer(InvocationOnMock invocation) throws InterruptedException {
                Thread.sleep(TIMEOUT_MS);
                return "ABCD1234";
              }
            });
    reconciliationService.process(null);
  }

  @Test
  public void testProcessTransactionListUploadBatch() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenReturn(generateDummyTransactionDetails());
    when(authorizationServerService.processSettlement(any()))
        .thenReturn(setupSettlementResponse95());
    when(authorizationServerService.uploadBatch(any())).thenReturn(setupBatchUploadResponse00());
    reconciliationService.process(null);
  }

  @Test
  public void testProcessTransactionListUploadBatchThrowTimeout() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenReturn(generateDummyTransactionDetails());
    when(authorizationServerService.processSettlement(any()))
        .thenReturn(setupSettlementResponse95());
    when(authorizationServerService.uploadBatch(any()))
        .thenAnswer(
            new Answer<String>() {
              @Override
              public String answer(InvocationOnMock invocation) throws InterruptedException {
                Thread.sleep(TIMEOUT_MS);
                return "ABCD1234";
              }
            });
    reconciliationService.process(null);
  }

  @Test
  public void testProcessSettlementTrailer() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenReturn(generateDummyTransactionDetails());
    when(authorizationServerService.processSettlement(any()))
        .thenReturn(setupSettlementResponse95());
    when(authorizationServerService.processSettlementTrailer(any()))
        .thenReturn(setupSettlementTrailerResponse00());
    reconciliationService.process(null);
  }

  @Test
  public void testProcessSettlementTrailer96() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenReturn(generateDummyTransactionDetails());
    when(authorizationServerService.processSettlement(any()))
        .thenReturn(setupSettlementResponse95());
    when(authorizationServerService.processSettlementTrailer(any()))
        .thenReturn(setupSettlementTrailerResponse96());
    reconciliationService.process(null);
  }

  @Test
  public void testProcessSettlementTrailerTimeout() throws InterruptedException {
    when(transactionRepository.getAllSuccessfulTransactions())
        .thenReturn(generateDummyTransactionDetails());
    when(authorizationServerService.processSettlement(any()))
        .thenReturn(setupSettlementResponse95());
    when(authorizationServerService.processSettlementTrailer(any()))
        .thenAnswer(
            new Answer<String>() {
              @Override
              public String answer(InvocationOnMock invocation) throws InterruptedException {
                Thread.sleep(TIMEOUT_MS);
                return "ABCD1234";
              }
            });
    reconciliationService.process(null);
  }

  private SettlementTrailerResponse setupSettlementTrailerResponse00() {
    SettlementTrailerResponse response = new SettlementTrailerResponse();
    response.setResponseCode("00");
    return response;
  }

  private SettlementTrailerResponse setupSettlementTrailerResponse96() {
    SettlementTrailerResponse response = new SettlementTrailerResponse();
    response.setResponseCode("96");
    return response;
  }

  private BatchUploadResponse setupBatchUploadResponse00() {
    BatchUploadResponse response = new BatchUploadResponse();
    response.setResponseCode("00");
    return response;
  }

  private static SettlementResponse setupSettlementResponse00() {
    SettlementResponse response = new SettlementResponse();
    response.setTraceNo("traceNo");
    response.setLocalTransDate("202312");
    response.setLocalTransTime("1212");
    response.setRetrievalRefNo("123456");
    response.setAuthorizationCode("123456");
    response.setResponseCode("00");
    response.setTid("909090");
    response.setBatchNo("BAT001");
    return response;
  }

  private static SettlementResponse setupSettlementResponse96() {
    SettlementResponse response = new SettlementResponse();
    response.setTraceNo("traceNo");
    response.setLocalTransDate("202312");
    response.setLocalTransTime("1212");
    response.setRetrievalRefNo("123456");
    response.setAuthorizationCode("123456");
    response.setResponseCode("96");
    response.setTid("909090");
    response.setBatchNo("BAT001");
    return response;
  }

  private static SettlementResponse setupSettlementResponse95() {
    SettlementResponse response = new SettlementResponse();
    response.setTraceNo("traceNo");
    response.setLocalTransDate("202312");
    response.setLocalTransTime("1212");
    response.setRetrievalRefNo("123456");
    response.setAuthorizationCode("123456");
    response.setResponseCode("95");
    response.setTid("909090");
    response.setBatchNo("BAT001");
    return response;
  }

  private static List<TransactionDetail> generateDummyTransactionDetails() {
    List<TransactionDetail> transactionDetails = new ArrayList<>();

    transactionDetails.add(
        createDummyTransactionDetail(
            "1",
            BigDecimal.valueOf(100.0),
            "1234567890123456",
            "abcdefg123456789",
            "USD",
            "1223",
            "MERCHANT123",
            "REQ123",
            "2023-07-01 10:00:00",
            "00",
            "2023-07-01 10:00:00",
            "AUTH123",
            "SALE",
            "REF123",
            "123456"));
    transactionDetails.add(
        createDummyTransactionDetail(
            "2",
            BigDecimal.valueOf(100.0),
            "1234567890123456",
            "abcdefg123456789",
            "USD",
            "1223",
            "MERCHANT123",
            "REQ123",
            "2023-07-01 10:00:00",
            "00",
            "2023-07-01 10:00:00",
            "AUTH123",
            "SALE",
            "REF123",
            "123456"));

    transactionDetails.add(
        createDummyTransactionDetail(
            "3",
            BigDecimal.valueOf(300.0),
            "5555666677778888",
            "qwertyuiopasdfgh",
            "GBP",
            "0412",
            "MERCHANT789",
            "REQ789",
            "2023-07-03 12:00:00",
            "00",
            "2023-07-03 12:00:00",
            "AUTH789",
            "SALE",
            "REF789",
            "987654"));
    transactionDetails.add(
        createDummyTransactionDetail(
            "4",
            BigDecimal.valueOf(200.0),
            "1234567890123456",
            "abcdefg123456789",
            "USD",
            "1223",
            "MERCHANT123",
            "REQ123",
            "2023-07-01 10:00:00",
            "00",
            "2023-07-01 10:00:00",
            "AUTH123",
            "SALE",
            "REF123",
            "123456"));
    transactionDetails.add(
        createDummyTransactionDetail(
            "5",
            BigDecimal.valueOf(4500.0),
            "1234567890123456",
            "abcdefg123456789",
            "USD",
            "1223",
            "MERCHANT123",
            "REQ123",
            "2023-07-01 10:00:00",
            "00",
            "2023-07-01 10:00:00",
            "AUTH123",
            "SALE",
            "REF123",
            "123456"));

    return transactionDetails;
  }

  private static TransactionDetail createDummyTransactionDetail(
      String transactionId,
      BigDecimal amount,
      String cardNumber,
      String cardToken,
      String currency,
      String expiryDate,
      String merchantAccountId,
      String requestId,
      String requestTimestamp,
      String responseCode,
      String transactionDateTime,
      String authorizationCode,
      String transactionType,
      String retRefNo,
      String sysTraceNo) {
    TransactionDetail transactionDetail = new TransactionDetail();
    transactionDetail.setId(Long.valueOf(transactionId));
    transactionDetail.setRequestedAmount(amount);
    transactionDetail.setCardNumber(cardNumber);
    transactionDetail.setCardTokenId(cardToken);
    transactionDetail.setRequestedAmountCurrency(currency);
    transactionDetail.setExpiryDate(expiryDate);
    transactionDetail.setMerchantAccountId(merchantAccountId);
    transactionDetail.setRequestId(requestId);
    transactionDetail.setRequestTimeStamp(fromInstantString(requestTimestamp));
    transactionDetail.setResponseCode(responseCode);
    transactionDetail.setCompletionTimeStamp(Timestamp.valueOf(transactionDateTime));
    transactionDetail.setAuthorizationCode(authorizationCode);
    transactionDetail.setTransactionType(transactionType);
    transactionDetail.setRetRefNo(retRefNo);
    transactionDetail.setSysTraceNo(sysTraceNo);

    return transactionDetail;
  }

  private static Instant fromInstantString(String stringDate) {
    // String stringDate = "2023-07-01 10:00:00";
    String pattern = "yyyy-MM-dd HH:mm:ss";
    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(pattern);
    LocalDateTime localDateTime = LocalDateTime.parse(stringDate, dateTimeFormatter);
    ZoneId zoneId = ZoneId.of("Asia/Singapore");
    ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
    return zonedDateTime.toInstant();
  }
}
