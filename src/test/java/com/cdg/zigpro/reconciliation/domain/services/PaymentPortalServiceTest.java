package com.cdg.zigpro.reconciliation.domain.services;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

import com.cdg.zigpro.reconciliation.domain.models.ReconciliationJobParam;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class PaymentPortalServiceTest {

  @Mock private IReconciliationRepository reconciliationRepository;

  @Mock private IReconciliationService reconciliationService;

  @InjectMocks private PaymentPortalService paymentPortalService;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void testGetBatchProcessByBatchNo() {
    String batchNo = "BATCH123";
    BatchProcess mockBatchProcess = new BatchProcess();

    when(reconciliationRepository.getBatchProcessByBatchNo(batchNo)).thenReturn(mockBatchProcess);

    BatchProcess result = paymentPortalService.getBatchProcessByBatchNo(batchNo);

    assertNotNull(result);
    assertEquals(mockBatchProcess, result);
    verify(reconciliationRepository, times(1)).getBatchProcessByBatchNo(batchNo);
  }

  @Test
  void testExecuteSettlementProcess() throws InterruptedException {
    ReconciliationJobParam jobParam = new ReconciliationJobParam();

    paymentPortalService.executeSettlementProcess(jobParam);

    verify(reconciliationService, times(1)).process(jobParam);
  }
}
