package com.cdg.zigpro.reconciliation.domain.helpers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.Test;

class StringUtilTest {

  @Test
  void testAddDecimalPoint() {
    // Valid case
    assertEquals("0000000010.50", StringUtil.addDecimalPoint("000000001050"));
    assertEquals("0000000000.00", StringUtil.addDecimalPoint("000000000000"));

    // Invalid case
    assertThrows(
        IllegalArgumentException.class,
        () -> {
          StringUtil.addDecimalPoint("00000000105"); // length is 11
        });
    assertThrows(
        IllegalArgumentException.class,
        () -> {
          StringUtil.addDecimalPoint("0000000010500"); // length is 13
        });
  }

  @Test
  void testAddDecimalPoint2() {
    // Valid case
    assertEquals("0010.50", StringUtil.addDecimalPoint2("001050"));
    assertEquals("0000.00", StringUtil.addDecimalPoint2("000000"));

    // Invalid case
    assertThrows(
        IllegalArgumentException.class,
        () -> {
          StringUtil.addDecimalPoint2("00105"); // length is 5
        });
    assertThrows(
        IllegalArgumentException.class,
        () -> {
          StringUtil.addDecimalPoint2("0010500"); // length is 7
        });
  }

  @Test
  void testAddDecimalPoint3() {
    // Valid case
    assertEquals("0.50", StringUtil.addDecimalPoint3("50"));
    assertEquals("1.00", StringUtil.addDecimalPoint3("100"));
    assertEquals("10.00", StringUtil.addDecimalPoint3("1000"));

    // Testing with 1 or 2 digit numbers
    assertEquals("0.01", StringUtil.addDecimalPoint3("1"));
    assertEquals("0.99", StringUtil.addDecimalPoint3("99"));

    // Testing with more than 2 digits
    assertEquals("1.00", StringUtil.addDecimalPoint3("100"));
    assertEquals("12.34", StringUtil.addDecimalPoint3("1234"));

    // Invalid cases
    assertEquals("0.00", StringUtil.addDecimalPoint3("00")); // Edge case with 2 zeros
    assertEquals("10.00", StringUtil.addDecimalPoint3("1000")); // 4 digits

    // No exception thrown in this method based on provided code.
  }

  @Test
  void testAppendLeft() {
    // Valid cases
    assertEquals("000001", StringUtil.appendLeft("1", 6, "0"));
    assertEquals("000000", StringUtil.appendLeft("000000", 6, "0"));
    assertEquals("  123", StringUtil.appendLeft("123", 5, " "));

    // Edge cases
    assertEquals("123", StringUtil.appendLeft("123", 3, "0")); // no padding needed

    // Test with different lengths
    assertEquals("001234", StringUtil.appendLeft("1234", 6, "0"));
    assertEquals("   12", StringUtil.appendLeft("12", 5, " "));

    // Invalid cases (not really applicable for this method since it doesn't throw exceptions)
  }
}
