
# AS Server Configuration
#as.server.ip=***********
#use one of four AS server ip for reconciliation directly
as.server.ip=**********
as.server.port=33333
as.iso.file=iso87binary-CREDIT.xml

settlement.terminalId=10000001
settlement.merchantId=198102368
spring.config.import=optional:aws-secretsmanager:/secrets/cabcharge/prod;/secrets/cabcharge/prod/cabcharge-gw-recon
# scheduler configuration
scheduler.reconciliation.cron=0 00 22 * * *
scheduler.reconciliation.timezone=GMT+8:00