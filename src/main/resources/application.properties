server.port=8080

# Database configuration
spring.datasource.url=********************************************
spring.datasource.username=read-from-env
spring.datasource.password=read-from-env
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.hibernate.ddl-auto=none

# MyBatis configuration
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.use-generated-keys=true

as.server.ip=read-from-env
as.server.port=read-from-env
as.iso.file=iso87binary-CREDIT.xml
settlement.output.file.path=/data/

settlement.request.batch.timeout.second=1800
settlement.request.batch.max.retry.attempt=3
settlement.request.timeout.second=3
settlement.request.max.retry.attempt=3

settlement.terminalId=read-from-env
settlement.merchantId=read-from-env
management.endpoints.web.exposure.include=health,prometheus

