package com.cdg.zigpro.reconciliation.domain.services;

import com.cdg.zigpro.reconciliation.domain.models.ReconciliationJobParam;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentPortalService {

  private final IReconciliationRepository reconciliationRepository;
  private final IReconciliationService reconciliationService;

  public BatchProcess getBatchProcessByBatchNo(String batchNo) {
    return reconciliationRepository.getBatchProcessByBatchNo(batchNo);
  }

  @Async
  public void executeSettlementProcess(ReconciliationJobParam jobParam)
      throws InterruptedException {
    reconciliationService.process(jobParam);
  }
}
