package com.cdg.zigpro.reconciliation.domain.models.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Data
@Table(name = "batch_process")
public class BatchProcess {

  @Id private UUID id;

  @Column(name = "batch_no", unique = true)
  private String batchNo;

  @Column(name = "terminal_id")
  private String terminalId;

  @Column(name = "merchant_id")
  private String merchantId;

  @Column(name = "sales_total_count")
  private Integer salesTotalCount;

  @Column(name = "sales_total_amount")
  private BigDecimal salesTotalAmount;

  @Column(name = "authorization_code")
  private String authorizationCode;

  @Column(name = "retrieval_ref_no")
  private String retrievalRefNo;

  @Column(name = "response_code")
  private String responseCode;

  @Column(name = "trace_no")
  private String traceNo;

  @Column(name = "status")
  private String status;

  @Column(name = "settlement_file")
  private String settlementFile;

  @Column(name = "created_at")
  @CreationTimestamp
  private Instant createdAt;

  @Column(name = "updated_at")
  @UpdateTimestamp
  private Instant updatedAt;

  @Column(name = "created_by")
  private String createdBy;

  @Column(name = "updated_by")
  private String updatedBy;
}
