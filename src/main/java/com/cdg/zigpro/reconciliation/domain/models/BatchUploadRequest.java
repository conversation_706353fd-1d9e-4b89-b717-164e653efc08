package com.cdg.zigpro.reconciliation.domain.models;

import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BatchUploadRequest {
  private String mid;
  private String tid;
  private String cardNo;
  private String expiryDate;

  private BigDecimal amount;

  private String locaTransTime;
  private String localTransDate;

  private String traceNo;
  private String retrievalRefNo;
  private String authorizationCode;
  private String responseCode;
  private String privateData;
  private String posConditionalMode;
  private String IsoAmount;
}
