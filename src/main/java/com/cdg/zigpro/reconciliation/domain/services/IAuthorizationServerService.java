package com.cdg.zigpro.reconciliation.domain.services;

import com.cdg.zigpro.reconciliation.domain.models.BatchUploadRequest;
import com.cdg.zigpro.reconciliation.domain.models.BatchUploadResponse;
import com.cdg.zigpro.reconciliation.domain.models.SettlementRequest;
import com.cdg.zigpro.reconciliation.domain.models.SettlementResponse;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTrailerRequest;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTrailerResponse;
import java.io.IOException;
import org.jpos.iso.ISOException;

public interface IAuthorizationServerService {
  public SettlementResponse processSettlement(SettlementRequest request);

  public BatchUploadResponse uploadBatch(BatchUploadRequest request);

  public SettlementTrailerResponse processSettlementTrailer(SettlementTrailerRequest request);

  public void initChannels() throws ISOException, IOException;

  public void disconnect();

  public void reconnectChannels();
}
