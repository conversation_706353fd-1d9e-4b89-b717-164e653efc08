package com.cdg.zigpro.reconciliation.domain.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.annotation.PostConstruct;
import java.io.File;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/** The type Health Check controller. */
@RestController
@Slf4j
public class HealthCheckController {

  @Value("${settlement.output.file.path}")
  private String SETTLEMENT_FILE_PATH;

  @GetMapping("/healthcheck")
  @Operation(summary = "Home API")
  @ApiResponses(
      value = {
        @ApiResponse(
            responseCode = "200",
            description = "Application is running!",
            content = @Content),
      })
  public ResponseEntity<String> getResponse() {
    var message = "Application is running!";
    return ResponseEntity.ok(message);
  }

  @PostConstruct
  public void init() {
    // Check if Recon data folder is available
    log.info("Listing files in {}", SETTLEMENT_FILE_PATH);
    // Create a File object
    File folder = new File(SETTLEMENT_FILE_PATH);

    // Check if the folder exists and is a directory
    if (folder.exists() && folder.isDirectory()) {
      // List all files and directories in the folder
      File[] listOfFiles = folder.listFiles();

      if (listOfFiles != null && listOfFiles.length > 0) {
        System.out.println("Files in folder \"" + SETTLEMENT_FILE_PATH + "\":");
        for (File file : listOfFiles) {
          if (file.isFile()) {
            System.out.println("File: " + file.getName());
          } else if (file.isDirectory()) {
            System.out.println("Directory: " + file.getName());
          }
        }
      } else {
        System.out.println("The folder is empty.");
      }
    } else {
      System.out.println("The specified folder path does not exist or is not a directory.");
    }
  }
}
