package com.cdg.zigpro.reconciliation.domain.utils;

/** Utility class for masking sensitive data for logging purposes. */
public class MaskingUtil {

  /**
   * Masks a Primary Account Number (PAN), showing only the first 6 and last 4 digits. Conforms to
   * PCI DSS standards for displaying PANs.
   *
   * @param pan The PAN to mask.
   * @return The masked PAN, or an empty string if the input is null or too short.
   */
  public static String maskPan(String pan) {
    if (pan == null || pan.length() <= 10) {
      return "******"; // Return a generic mask if PAN is invalid or too short
    }
    return pan.substring(0, 6) + "******" + pan.substring(pan.length() - 4);
  }

  /**
   * Returns a generic mask for highly sensitive data that should never be logged.
   *
   * @return A string of asterisks.
   */
  public static String maskEntireValue() {
    return "******";
  }
}
