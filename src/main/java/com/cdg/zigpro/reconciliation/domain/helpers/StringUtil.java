package com.cdg.zigpro.reconciliation.domain.helpers;

public class StringUtil {
  /**
   * To add decimal point at the 11th position of the String parameter.
   *
   * @param amount Transaction amount in String length of 12 e.g. $10.50 -> 000000001050
   * @return Formatted amount in String
   * @throws IllegalArgumentException thrown when parameter's length != 12
   */
  public static synchronized String addDecimalPoint(String amount) throws IllegalArgumentException {
    if (amount.length() != 12) throw new IllegalArgumentException();
    else return amount.substring(0, 10) + "." + amount.substring(10, 12);
  }

  /**
   * To add decimal point at the 5th position of the String parameter.
   *
   * @param amount Transaction amount in String length of 6 e.g. $10.50 -> 001050
   * @return Formatted amount in String
   * @throws IllegalArgumentException thrown when parameter's length != 6
   */
  public static synchronized String addDecimalPoint2(String amount)
      throws IllegalArgumentException {
    if (amount.length() != 6) throw new IllegalArgumentException();
    else return amount.substring(0, 4) + "." + amount.substring(4, 6);
  }

  /**
   * To add decimal point at the 2nd position counting from the back of the String parameter.
   *
   * @param amount Transaction amount in String length of 2 e.g. 50 -> 0.50
   * @return Formatted amount in String
   * @throws IllegalArgumentException thrown when parameter is not in number
   */
  public static synchronized String addDecimalPoint3(
      String amount) { // throws IllegalArgumentException{
    if (amount.length() < 3) {
      String amount1 = amount;
      amount1 = addDecimalPoint3("0" + amount1);
      return amount1;
    } else {
      String amount2 = amount;
      amount2 =
          amount2.substring(0, amount2.length() - 2)
              + "."
              + amount2.substring(amount2.length() - 2, amount2.length());
      return amount2;
    }
  }

  /**
   * This method works like LPAD/RPAD of oracle. It will append one str and the other towards the
   * left hand side making the str to the intended length. Example appendLeft("1",6,"0") returns
   * "000001".
   *
   * @param str1 Original String to be appended
   * @param length Intended String length
   * @param str2 String to be appended on the left
   * @return Appended String
   */
  public static synchronized String appendLeft(String str1, int length, String str2) {
    int lengthToAppend = length - str1.length();
    for (int i = 0; i < lengthToAppend; i++) {
      str1 = str2 + str1;
    }

    return str1;
  }
}
