package com.cdg.zigpro.reconciliation.domain.models;

import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SettlementRequest {
  private String mid;
  private String tid;
  private String batchNo;
  private String traceNo;
  private Integer salesTotalCount;
  private BigDecimal salesTotalAmount;
  private Integer refundCount;
  private BigDecimal refundAmount;
  private Integer debitSalesCount;
  private BigDecimal debitSalesAmount;
  private Integer preAuthCount;
  private BigDecimal preAuthAmount;
  private Integer preAuthRefundCount;
  private BigDecimal preAuthRefundAmount;
}
