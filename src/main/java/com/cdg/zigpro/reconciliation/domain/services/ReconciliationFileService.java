package com.cdg.zigpro.reconciliation.domain.services;

import com.cdg.zigpro.reconciliation.domain.helpers.Utils;
import com.cdg.zigpro.reconciliation.domain.models.DepositFileHeaderRecord;
import com.cdg.zigpro.reconciliation.domain.models.DepositFileTrailerRecord;
import com.cdg.zigpro.reconciliation.domain.models.MerchantItemDetailRecord;
import com.cdg.zigpro.reconciliation.domain.models.ReconciliationJobParam;
import com.cdg.zigpro.reconciliation.domain.models.SettlementFile;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTnxResult;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import com.cdg.zigpro.reconciliation.domain.models.entities.TransactionDetail;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class ReconciliationFileService implements IReconciliationFileService {
  private static final String FIELD_TYPE_AN = "A/N";
  private static final String FIELD_TYPE_N = "N";

  @Value("${settlement.output.file.path}")
  private String SETTLEMENT_FILE_PATH;

  private IReconciliationRepository reconciliationRepository;

  public ReconciliationFileService(IReconciliationRepository reconciliationRepository) {
    this.reconciliationRepository = reconciliationRepository;
  }

  private void generateSettlementFile(SettlementFile settlementFile, String filePath) {
    log.info("generate Settlement File into  Path:{}", filePath);

    try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
      // Write header
      writeHeaderRecord(settlementFile.getHeader(), writer);

      // Write records
      List<MerchantItemDetailRecord> records = settlementFile.getRecords();
      log.info("Merchant Item Detail Records: {}", records.size());
      for (MerchantItemDetailRecord record : records) {
        writeMerchantItemDetailRecord(record, writer);
      }

      // Write trailer
      writeTrailerRecord(settlementFile.getTrailer(), writer);
      log.info("Settlement File generated successfully.");
    } catch (IOException e) {
      // Handle the exception as needed
      log.error("Exception occurs while generate reconciliation file: " + e.getMessage());
    }
  }

  private void writeHeaderRecord(DepositFileHeaderRecord header, BufferedWriter writer)
      throws IOException {
    writer.write(formatField(header.getRecordType(), 3, FIELD_TYPE_AN));
    writer.write(formatField(header.getFileCreationDate(), 8, FIELD_TYPE_N));
    writer.write(formatField(header.getFileCreationTime(), 4, FIELD_TYPE_N));
    writer.write(formatField("", 375, FIELD_TYPE_AN)); // Filler
    writer.newLine();
  }

  private void writeMerchantItemDetailRecord(MerchantItemDetailRecord record, BufferedWriter writer)
      throws IOException {
    writer.write(formatField(record.getRecordType(), 3, FIELD_TYPE_AN));
    writer.write(formatField(record.getJobNo(), 10, FIELD_TYPE_N));
    writer.write(formatField(record.getVehicleNo(), 12, FIELD_TYPE_AN));
    writer.write(formatField(record.getDriverIC(), 9, FIELD_TYPE_AN));
    writer.write(formatField(record.getTransactionDateTime(), 14, FIELD_TYPE_AN));
    writer.write(formatField(record.getPaymentMode(), 4, FIELD_TYPE_AN));
    writer.write(formatField(record.getCardNo(), 19, FIELD_TYPE_AN));
    writer.write(formatField(record.getApprovalCode(), 6, FIELD_TYPE_AN));
    writer.write(formatField(record.getFareAmount(), 6, FIELD_TYPE_N));
    writer.write(formatField(record.getAdminAmount(), 6, FIELD_TYPE_N));
    writer.write(formatField(record.getGstAmount(), 6, FIELD_TYPE_N));
    writer.write(formatField(record.getAmountOfTrans(), 12, FIELD_TYPE_N));
    writer.write(formatField(record.getTerminalID(), 8, FIELD_TYPE_AN));
    writer.write(formatField(record.getMerchantID(), 15, FIELD_TYPE_AN));
    writer.write(formatField(record.getSystemTraceNo(), 6, FIELD_TYPE_N));
    writer.write(formatField(record.getCompanyCode(), 4, FIELD_TYPE_AN));
    writer.write(formatField(record.getTripStart(), 15, FIELD_TYPE_AN));
    writer.write(formatField(record.getTripEnd(), 15, FIELD_TYPE_AN));
    writer.write(formatField(record.getPickupAddress(), 80, FIELD_TYPE_AN));
    writer.write(formatField(record.getDestination(), 80, FIELD_TYPE_AN));
    writer.write(formatField(record.getSalesDraftNumber(), 15, FIELD_TYPE_AN));
    writer.write(formatField(record.getCardType(), 1, FIELD_TYPE_AN));
    writer.write(formatField(record.getTransactionSettlementDate(), 8, FIELD_TYPE_AN));
    writer.write(formatField(record.getBankTID(), 8, FIELD_TYPE_AN));
    writer.write(formatField(record.getBankBatchCloseNo(), 6, FIELD_TYPE_AN));
    writer.write(formatField(record.getOfflineTransactionDateTime(), 14, FIELD_TYPE_AN));
    writer.write(formatField(record.getOfflineIndicator(), 1, FIELD_TYPE_AN));
    writer.write(formatField("", 8, FIELD_TYPE_AN)); // Filler
    writer.newLine();
  }

  private void writeTrailerRecord(DepositFileTrailerRecord trailer, BufferedWriter writer)
      throws IOException {
    writer.write(formatField(trailer.getRecordType(), 3, FIELD_TYPE_AN));
    writer.write(formatField(trailer.getFileCreationDate(), 8, FIELD_TYPE_AN));
    writer.write(formatField(trailer.getEndOfFileCreationTime(), 4, FIELD_TYPE_N));
    writer.write(formatField(trailer.getTotalRecordCount(), 8, FIELD_TYPE_N));
    writer.write(formatField("", 367, FIELD_TYPE_AN)); // Filler
    writer.newLine();
  }

  private String formatField(String fieldValue, int fieldLength, String fieldType) {
    fieldValue = fieldValue != null ? fieldValue : "";
    if (fieldType.equals(FIELD_TYPE_AN)) {
      fieldValue = String.format("%-" + fieldLength + "s", fieldValue);
    } else if (fieldType.equals(FIELD_TYPE_N)) {
      fieldValue = String.format("%" + fieldLength + "s", fieldValue).replace(' ', '0');
    }
    return fieldValue.substring(0, fieldLength);
  }

  @Override
  public void generateSettlementFile(
      String batchId,
      HashMap<String, SettlementTnxResult> batchTransactionsResults,
      List<TransactionDetail> transactions,
      ReconciliationJobParam jobParam) {
    var settledTranactions = new ArrayList<TransactionDetail>();
    log.info("Total transactions: " + transactions.size());
    log.info("Total batchTransactionsResults: " + batchTransactionsResults.size());

    for (var t : transactions) {
      var tnxResult = batchTransactionsResults.get(t.getId().toString());
      if (Boolean.TRUE.equals(tnxResult.getSettled())) {
        settledTranactions.add(t);
      }
    }
    log.info("Total settledTranactions: " + settledTranactions.size());
    String settlementDate = Utils.getCurrentDateInYYYMMDD();

    var header = new DepositFileHeaderRecord();
    header.setRecordType("HDR");
    header.setFileCreationDate(settlementDate);
    header.setFileCreationTime(Utils.getCurrentTimeInHHMM());

    var trailer = new DepositFileTrailerRecord();
    trailer.setRecordType("TRL");
    trailer.setFileCreationDate(settlementDate);
    trailer.setEndOfFileCreationTime(Utils.getCurrentTimeInHHMM());
    trailer.setTotalRecordCount("" + settledTranactions.size());

    var records = new ArrayList<MerchantItemDetailRecord>();
    for (var t : settledTranactions) {
      var record = new MerchantItemDetailRecord();
      record.setRecordType("DTL");
      record.setJobNo(t.getJobNo());
      record.setVehicleNo(t.getVehicleId());
      record.setDriverIC(t.getDriverId());
      record.setTransactionDateTime(Utils.convertToTimestampString(t.getCompletionTimeStamp()));
      if (isCabc(t.getCardNumber())) {
        record.setPaymentMode("CABC");
        record.setCardNo(t.getCardNumber());
      } else if (isEvch(t.getCardNumber())) {
        record.setPaymentMode("EVCH");
        record.setCardNo(
            StringUtils.hasLength(t.getCardNumber())
                ? t.getCardNumber()
                : truncatePAN(t.getTrack2Data()));
      } else {
        // ..Todo : handle other payment modes
        log.error(
            "Fail to set Payment mode for Card No:{}, track2Data:{}",
            t.getCardNumber(),
            t.getTrack2Data());
      }
      record.setApprovalCode(t.getAuthorizationCode());
      record.setFareAmount(t.getFareAmount());
      record.setAdminAmount(t.getAdminFee());
      record.setGstAmount(t.getGstAmount());
      record.setAmountOfTrans(Utils.convertAmountToIsoString(t.getRequestedAmount()));
      record.setTerminalID(t.getTerminalId());
      record.setMerchantID(t.getMerchantId());
      record.setSystemTraceNo(t.getSysTraceNo());
      record.setCompanyCode("CTPL");
      record.setCardType("C");
      record.setTransactionSettlementDate(Utils.getCurrentDateInYYYMMDD());
      record.setBankTID("********"); // todo: check with cubepay for bank tid
      record.setBankBatchCloseNo(batchId);
      record.setOfflineTransactionDateTime(
          "Y".equals(t.getOfflineIndicator())
              ? Utils.convertToTimestampString(t.getCompletionTimeStamp())
              : "");
      record.setOfflineIndicator(t.getOfflineIndicator());
      records.add(record);
    }
    log.info("Total records: " + records.size());

    var settlementFile = new SettlementFile();
    settlementFile.setHeader(header);
    settlementFile.setRecords(records);
    settlementFile.setTrailer(trailer);

    // Create the file name
    String fileName;
    if (jobParam == null) {
      fileName = "CABC_" + settlementDate + ".txt";
    } else {
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
      String executionTime =
          jobParam
              .getExecutionTime()
              .atZone(ZoneId.of("UTC"))
              .withZoneSameInstant(ZoneId.of("Asia/Singapore"))
              .format(formatter);
      fileName = "CABC_" + executionTime + "_batch_" + batchId + ".txt";
    }
    generateSettlementFile(settlementFile, SETTLEMENT_FILE_PATH + fileName);

    // Set value for batch_process settlement file name
    updateBatchReconFileName(batchId, fileName);

    log.info("Settlement file generated: " + fileName);
  }

  /** isEvch: Check E-Voucher condition with card number length is 10 digits and start with '1'. */
  private boolean isEvch(String cardNumber) {
    return !StringUtils.hasLength(cardNumber)
        || (cardNumber.length() == 10 && cardNumber.indexOf("1") >= 0);
  }

  private boolean isCabc(String cardNumber) {
    return StringUtils.hasLength(cardNumber) && cardNumber.length() == 16;
  }

  private void updateBatchReconFileName(String batchId, String fileName) {
    log.info("Update batch_process settlement file name: " + batchId + " - " + fileName);
    BatchProcess batchProcess = new BatchProcess();
    batchProcess.setBatchNo(batchId);
    batchProcess.setSettlementFile(fileName);
    reconciliationRepository.updateBatchReconFileName(batchProcess);
  }

  private String truncatePAN(String track2Data) {
    if (!StringUtils.hasLength(track2Data)) {
      return "";
    }
    StringBuffer temp = new StringBuffer();
    for (int i = 0; i < track2Data.length(); i++) {
      char character = track2Data.charAt(i);
      // Stop after hitting space
      if (character == '=') break;
      temp.append(character);
    }
    return temp.toString();
  }
}
