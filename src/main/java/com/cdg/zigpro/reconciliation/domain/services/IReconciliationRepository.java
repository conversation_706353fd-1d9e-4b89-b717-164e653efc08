package com.cdg.zigpro.reconciliation.domain.services;

import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchTransaction;
import java.util.List;

public interface IReconciliationRepository {
  public void insertBatchProcess(BatchProcess batchProcess);

  public Long getBatchProcessSequence();

  public void updateBatchProcess(BatchProcess batchProcess);

  public BatchProcess getBatchProcessByBatchNo(String batchNo);

  public void insertBatchTransactions(List<BatchTransaction> batchTransactions);

  void updateBatchReconFileName(BatchProcess batchProcess);
}
