package com.cdg.zigpro.reconciliation.domain.services;

import com.cdg.zigpro.reconciliation.domain.helpers.Utils;
import com.cdg.zigpro.reconciliation.domain.models.BatchUploadRequest;
import com.cdg.zigpro.reconciliation.domain.models.BatchUploadResponse;
import com.cdg.zigpro.reconciliation.domain.models.ReconciliationJobParam;
import com.cdg.zigpro.reconciliation.domain.models.SettledBatch;
import com.cdg.zigpro.reconciliation.domain.models.SettlementRequest;
import com.cdg.zigpro.reconciliation.domain.models.SettlementResponse;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTnxResult;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTrailerRequest;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTrailerResponse;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchTransaction;
import com.cdg.zigpro.reconciliation.domain.models.entities.TransactionDetail;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ReconciliationService implements IReconciliationService {

  @Value("${settlement.request.max.retry.attempt}")
  private int MAX_RETRY_ATTEMPTS;

  @Value("${settlement.request.timeout.second}")
  private int TIMEOUT_SECONDS;

  @Value("${settlement.request.batch.max.retry.attempt}")
  private int BATCH_MAX_RETRY_ATTEMPTS;

  @Value("${settlement.request.batch.timeout.second}")
  private int BATCH_TIMEOUT_SECONDS;

  @Value("${settlement.terminalId}")
  private String SETTLEMENT_TERMINAL_ID;

  @Value("${settlement.merchantId}")
  private String SETTLEMENT_MERCHANT_ID;

  private ITransactionRepository transactionRepository;
  private IReconciliationRepository reconciliationRepository;
  private IAuthorizationServerService authorizationServerService;
  private IReconciliationFileService reconciliationFileService;
  private final Executor taskExecutor;

  public ReconciliationService(
      ITransactionRepository transactionRepository,
      IReconciliationRepository reconciliationRepository,
      IAuthorizationServerService authorizationServerService,
      IReconciliationFileService reconciliationFileService,
      @Qualifier("reconciliationTaskExecutor") Executor taskExecutor) {

    this.transactionRepository = transactionRepository;
    this.reconciliationRepository = reconciliationRepository;
    this.authorizationServerService = authorizationServerService;
    this.reconciliationFileService = reconciliationFileService;
    this.taskExecutor = taskExecutor;
  }

  @Override
  public void process(ReconciliationJobParam jobParam) throws InterruptedException {
    log.info("Started reconciliation process...");
    try {
      List<TransactionDetail> successfulTransactions = new ArrayList<TransactionDetail>();

      String startReconDatetime = "";
      String endReconDatetime = "";
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");

      if (jobParam == null) {
        log.info("No jobParam found, processing all successful transactions");
        // Transactions for previous day until current day
        ZonedDateTime sgTime = Instant.now().atZone(ZoneId.of("Asia/Singapore"));
        startReconDatetime = sgTime.toLocalDate().minusDays(1).atStartOfDay().format(formatter);
        endReconDatetime = sgTime.toLocalDate().atTime(23, 59, 59).format(formatter);
      } else {
        // Transactions for selected input day only
        ZonedDateTime zonedDateTime =
            jobParam
                .getExecutionTime()
                .atZone(ZoneId.of("UTC"))
                .withZoneSameInstant(ZoneId.of("Asia/Singapore"));
        startReconDatetime =
            zonedDateTime.toLocalDate().minusDays(1).atStartOfDay().format(formatter);
        endReconDatetime = zonedDateTime.toLocalDate().atTime(23, 59, 59).format(formatter);
      }

      log.info(
          "Reconciliation process started for datetime {} - {}",
          startReconDatetime,
          endReconDatetime);
      successfulTransactions =
          transactionRepository.getAllSuccessfulTransactions(startReconDatetime, endReconDatetime);

      if (!successfulTransactions.isEmpty()) {
        log.info("Found %s records for reconciliation".formatted(successfulTransactions.size()));

        authorizationServerService.initChannels();
        var batchNo = generateBatchNo();
        checkpointBatchProcess(batchNo);

        var batchTransactionsResults = new HashMap<String, SettlementTnxResult>();
        SettledBatch settledBatch = null;

        var settlementResponse = submitSettlement(batchNo, successfulTransactions);
        if (settlementResponse.getResponseCode().equals("95")) {
          settledBatch = submitBatchUpload(successfulTransactions);
          submitSettlementTrailer(
              batchNo, settledBatch.getSalesTotalCount(), settledBatch.getSalesTotalAmount());
          batchTransactionsResults = settledBatch.getBatchTransactionsResult();
        } else if (settlementResponse.getResponseCode().equals("00")) {
          batchTransactionsResults = createBatchTransactionResults(successfulTransactions, true);
        } else {
          batchTransactionsResults = createBatchTransactionResults(successfulTransactions, false);
        }
        saveReconciliationResults(
            batchNo, batchTransactionsResults, settledBatch, settlementResponse);
        log.info("Writting settlement file for batchNo %s".formatted(batchNo));

        reconciliationFileService.generateSettlementFile(
            batchNo, batchTransactionsResults, successfulTransactions, jobParam);
        log.info("Done writting settlement file for batchNo %s".formatted(batchNo));

        authorizationServerService.disconnect();
      } else {
        log.info("No transaction records found");
      }
    } catch (InterruptedException e) {
      log.error("process interrupted");
      throw e;
    } catch (Exception e) {
      log.error("reconcilation process error:", e);
    } finally {
      log.info("reconciliation process done!");
    }
  }

  private String generateBatchNo() {
    Long batchProcessSeq = reconciliationRepository.getBatchProcessSequence();
    return String.format("%1$" + 6 + "s", batchProcessSeq % 1_000_000).replace(' ', '0');
  }

  private SettlementResponse submitSettlement(
      String batchNo, List<TransactionDetail> successfulTransactions)
      throws ExecutionException, InterruptedException {

    int salesTotalCount = 0;
    BigDecimal salesTotalAmount = BigDecimal.ZERO;

    // Calculate the total count and amount of successful sales
    for (TransactionDetail transaction : successfulTransactions) {
      salesTotalCount++;
      salesTotalAmount = salesTotalAmount.add(transaction.getRequestedAmount());
    }

    SettlementRequest request = new SettlementRequest();

    request.setSalesTotalCount(salesTotalCount);
    request.setSalesTotalAmount(salesTotalAmount);
    request.setMid(SETTLEMENT_MERCHANT_ID);
    request.setTid(SETTLEMENT_TERMINAL_ID);
    request.setBatchNo(batchNo);
    request.setTraceNo(Utils.generateRandomNumeric(6));

    int retryAttempts = 0;
    SettlementResponse response = new SettlementResponse();

    do {
      CompletableFuture<SettlementResponse> future =
          CompletableFuture.supplyAsync(
              () -> authorizationServerService.processSettlement(request), taskExecutor);
      try {

        log.info(
            "sending settlement batch no %s - %s"
                .formatted(request.getBatchNo(), request.getTraceNo()));
        response = future.get(BATCH_TIMEOUT_SECONDS, TimeUnit.SECONDS);
      } catch (TimeoutException e) {
        log.error("settlement batch no %s timedout".formatted(request.getBatchNo()));

        future.cancel(true); // Cancel the future if the timeout occurs
        response.setResponseCode("96");
      } catch (InterruptedException | ExecutionException e) {
        log.error("process interupted");
        response.setResponseCode("96");
        throw e;
      }

      // Check the response
      if (response != null && (response.getResponseCode().equals("96"))) {
        retryAttempts++;
        authorizationServerService.reconnectChannels();
        log.info("retry settlement at attempt " + retryAttempts);
      } else {
        log.info(
            "settlement %s - %s completed with resp code %s after attempt %s"
                .formatted(
                    request.getBatchNo(),
                    request.getTraceNo(),
                    response != null ? response.getResponseCode() : null,
                    retryAttempts));
        break;
      }
    } while (retryAttempts < BATCH_MAX_RETRY_ATTEMPTS);

    if (response != null) {
      response.setBatchNo(batchNo);
    }
    return response;
  }

  public SettledBatch submitBatchUpload(List<TransactionDetail> transactions)
      throws ExecutionException, InterruptedException {
    log.info("sending batch upload for %s transactions...".formatted(transactions.size()));

    BigDecimal salesTotalAmount = new BigDecimal(0);
    Integer salesTotalCount = 0;
    HashMap<String, SettlementTnxResult> batchTrasactionsResult = new HashMap<>();
    for (TransactionDetail transaction : transactions) {
      BatchUploadRequest request = createBatchUploadRequest(transaction);
      boolean success = uploadBatchWithRetry(request);
      log.info("submitted batch upload %s - success=%s".formatted(request.getTraceNo(), success));
      if (success) {
        salesTotalCount++;
        salesTotalAmount = salesTotalAmount.add(transaction.getRequestedAmount());
      }
      var tnxResult = new SettlementTnxResult();
      tnxResult.setMid(transaction.getMerchantId());
      tnxResult.setTid(transaction.getTerminalId());
      tnxResult.setSettled(success);
      batchTrasactionsResult.put(transaction.getId().toString(), tnxResult);
    }
    var settledBatch = new SettledBatch();
    settledBatch.setSalesTotalAmount(salesTotalAmount);
    settledBatch.setSalesTotalCount(salesTotalCount);
    settledBatch.setBatchTransactionsResult(batchTrasactionsResult);
    return settledBatch;
  }

  private BatchUploadRequest createBatchUploadRequest(TransactionDetail transaction) {
    BatchUploadRequest request = new BatchUploadRequest();
    request.setMid(transaction.getMerchantId());
    request.setTid(transaction.getTerminalId());
    request.setCardNo(transaction.getCardNumber());
    request.setExpiryDate(transaction.getExpiryDate());
    request.setAmount(transaction.getRequestedAmount());
    request.setTraceNo(transaction.getSysTraceNo());
    request.setRetrievalRefNo(transaction.getRetRefNo());
    request.setAuthorizationCode(transaction.getAuthorizationCode());
    request.setResponseCode(transaction.getResponseCode());

    return request;
  }

  private boolean uploadBatchWithRetry(BatchUploadRequest request)
      throws ExecutionException, InterruptedException {
    int attempt = 0;

    while (attempt < MAX_RETRY_ATTEMPTS) {
      attempt++;

      CompletableFuture<BatchUploadResponse> future =
          CompletableFuture.supplyAsync(
              () -> authorizationServerService.uploadBatch(request), taskExecutor);
      try {
        log.info("sending batch upload %s".formatted(request.getTraceNo()));

        BatchUploadResponse response = future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);

        if (response != null && !response.getResponseCode().equals("96")) {
          log.info("sent batch upload %s at attempt %s".formatted(request.getTraceNo(), attempt));
          return true; // Success
        }
      } catch (InterruptedException | ExecutionException e) {
        log.error(
            "Interruption or execution error when sending batch upload %s"
                .formatted(request.getTraceNo()),
            e);
        throw e;
      } catch (TimeoutException e) {
        future.cancel(true); // Cancel the task on timeout
        // Handle timeout
        if (attempt == MAX_RETRY_ATTEMPTS) {
          log.error("sending batch upload %s timedout".formatted(request.getTraceNo()));
          return false; // Failed after maximum attempts
        }
      }
      log.info("retrying batch upload %s at attempt %s".formatted(request.getTraceNo(), attempt));
    }

    return false; // Failed after maximum attempts
  }

  private SettlementTrailerResponse submitSettlementTrailer(
      String batchNo, int salesTotalCount, BigDecimal salesTotalAmount)
      throws ExecutionException, InterruptedException {

    SettlementTrailerRequest request = new SettlementTrailerRequest();

    request.setSalesTotalCount(salesTotalCount);
    request.setSalesTotalAmount(salesTotalAmount);
    request.setMid(SETTLEMENT_MERCHANT_ID);
    request.setTid(SETTLEMENT_TERMINAL_ID);
    request.setBatchNo(batchNo);
    request.setTraceNo(Utils.generateRandomNumeric(6));

    int retryAttempts = 0;
    SettlementTrailerResponse response = new SettlementTrailerResponse();

    do {
      CompletableFuture<SettlementTrailerResponse> future =
          CompletableFuture.supplyAsync(
              () -> authorizationServerService.processSettlementTrailer(request), taskExecutor);
      try {

        log.info(
            "sending settlement trailer batch no %s - %s".formatted(batchNo, request.getTraceNo()));
        response = future.get(BATCH_TIMEOUT_SECONDS, TimeUnit.SECONDS);
      } catch (TimeoutException e) {
        log.error("settlement trailer batch no %s timedout".formatted(request.getBatchNo()));

        future.cancel(true); // Cancel the future if the timeout occurs
        response.setResponseCode("96");
      } catch (InterruptedException | ExecutionException e) {
        log.error("settlement trailer process interupted");
        response.setResponseCode("96");
        throw e;
      }

      // Check the response
      if (response != null && (response.getResponseCode().equals("96"))) {
        retryAttempts++;
        authorizationServerService.reconnectChannels();
        log.info("retry settlement trailer at attempt " + retryAttempts);
      } else {
        log.info(
            "settlement trailer %s - %s completed with resp code %s after attempt %s"
                .formatted(
                    request.getBatchNo(),
                    request.getTraceNo(),
                    response != null ? response.getResponseCode() : null,
                    retryAttempts));
        break;
      }
    } while (retryAttempts < BATCH_MAX_RETRY_ATTEMPTS);

    if (response != null) {
      response.setTraceNo(request.getTraceNo());
    }
    return response;
  }

  private HashMap<String, SettlementTnxResult> createBatchTransactionResults(
      List<TransactionDetail> transactions, Boolean success) {

    HashMap<String, SettlementTnxResult> results = new HashMap<>();
    for (TransactionDetail transaction : transactions) {
      var tnxResult = new SettlementTnxResult();
      tnxResult.setMid(transaction.getMerchantId());
      tnxResult.setTid(transaction.getTerminalId());
      tnxResult.setSettled(success);
      results.put(transaction.getId().toString(), tnxResult);
    }
    return results;
  }

  private void checkpointBatchProcess(String batchNo) {
    log.info("batch process checking point...");

    var batchProcess = new BatchProcess();
    batchProcess.setBatchNo(batchNo);
    batchProcess.setStatus("STARTED");
    batchProcess.setCreatedBy("reconciliation_msc");
    batchProcess.setCreatedAt(Instant.now());
    reconciliationRepository.insertBatchProcess(batchProcess);

    log.info("batch process saved!");
  }

  private void saveReconciliationResults(
      String batchNo,
      HashMap<String, SettlementTnxResult> batchTransactionResults,
      SettledBatch settledBatch,
      SettlementResponse settlementResponse) {

    var batchProcess = new BatchProcess();
    batchProcess.setBatchNo(batchNo);
    batchProcess.setTerminalId(SETTLEMENT_TERMINAL_ID);
    batchProcess.setMerchantId(SETTLEMENT_MERCHANT_ID);
    batchProcess.setStatus("PROCESSED");
    if (settledBatch != null) {
      batchProcess.setSalesTotalCount(settledBatch.getSalesTotalCount());
      batchProcess.setSalesTotalAmount(settledBatch.getSalesTotalAmount());
    } else {
      batchProcess.setSalesTotalCount(0);
      batchProcess.setSalesTotalAmount(BigDecimal.ZERO);
    }
    if (settlementResponse != null) {
      batchProcess.setAuthorizationCode(settlementResponse.getAuthorizationCode());
      batchProcess.setRetrievalRefNo(settlementResponse.getRetrievalRefNo());
      batchProcess.setResponseCode(settlementResponse.getResponseCode());
      batchProcess.setTraceNo(settlementResponse.getTraceNo());
    }
    batchProcess.setUpdatedAt(Instant.now());
    batchProcess.setUpdatedBy("reconciliation_msc");
    reconciliationRepository.updateBatchProcess(batchProcess);

    log.info("batch no %s updated batch process status!".formatted(batchNo));

    List<BatchTransaction> batchTransactions = new ArrayList<>();
    for (var k : batchTransactionResults.entrySet()) {
      var batchTransaction = new BatchTransaction();
      batchTransaction.setBatchNo(batchNo);
      batchTransaction.setMerchantId(k.getValue().getMid());
      batchTransaction.setTerminalId(k.getValue().getTid());
      batchTransaction.setTransactionId(Long.valueOf(k.getKey()));
      batchTransaction.setStatus(
          Boolean.TRUE.equals(k.getValue().getSettled()) ? "CAPTURED" : "PENDING");
      batchTransaction.setDescription(
          Boolean.TRUE.equals(k.getValue().getSettled())
              ? "Transaction captured successfully"
              : "Transaction failed to capture");
      batchTransaction.setCreatedBy("reconciliation_msc");
      batchTransaction.setCreatedAt(Instant.now());
      batchTransaction.setUpdatedAt(Instant.now());
      batchTransactions.add(batchTransaction);
    }

    log.info("BatchTransactions Result size:{}", batchTransactions.size());

    // split batch transactions into chunks of 1000
    for (int i = 0; i < batchTransactions.size(); i += 1000) {
      log.info("BatchTransactions save to table for batch index:{}", i);
      List<BatchTransaction> chunk =
          batchTransactions.subList(i, Math.min(i + 1000, batchTransactions.size()));
      reconciliationRepository.insertBatchTransactions(chunk);
    }

    log.info("updated batch transactions status!");
  }
}
