package com.cdg.zigpro.reconciliation.domain.config;

import java.util.concurrent.Executor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ReconciliationConfig {

  /**
   * Creates a shared, managed thread pool for handling asynchronous reconciliation tasks. This
   * avoids the performance and memory issues of creating new threads for each task.
   *
   * @return A configured Executor bean.
   */
  @Bean(name = "reconciliationTaskExecutor")
  public Executor reconciliationTaskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(10); // Adjust based on expected workload
    executor.setMaxPoolSize(20);
    executor.setQueueCapacity(500);
    executor.setThreadNamePrefix("ReconTask-");
    executor.initialize();
    return executor;
  }
}
