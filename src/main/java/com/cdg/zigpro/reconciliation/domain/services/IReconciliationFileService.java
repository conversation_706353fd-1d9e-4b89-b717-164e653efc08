package com.cdg.zigpro.reconciliation.domain.services;

import com.cdg.zigpro.reconciliation.domain.models.ReconciliationJobParam;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTnxResult;
import com.cdg.zigpro.reconciliation.domain.models.entities.TransactionDetail;
import java.util.HashMap;
import java.util.List;

public interface IReconciliationFileService {
  public void generateSettlementFile(
      String batchId,
      HashMap<String, SettlementTnxResult> records,
      List<TransactionDetail> transactions,
      ReconciliationJobParam jobParam);
}
