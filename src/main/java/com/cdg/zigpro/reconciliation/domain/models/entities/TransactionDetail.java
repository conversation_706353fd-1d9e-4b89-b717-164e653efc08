package com.cdg.zigpro.reconciliation.domain.models.entities;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

@Entity
@Table(name = "payment_transaction")
@Data
public class TransactionDetail {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "payment_transaction_generator")
  @SequenceGenerator(
      name = "payment_transaction_generator",
      sequenceName = "payment_transaction_seq",
      allocationSize = 1)
  private Long id;

  private String merchantAccountId;
  private String requestId;
  private String transactionType;
  private String requestedAmountCurrency;
  private BigDecimal requestedAmount;

  @Column(length = 999)
  private String cdgTripInfo;

  private String cdgContactlessCardData;
  private String cardTokenId;
  private String paymentMethod;
  private String paymentSource;

  // Purchase info
  private String fareAmount;
  private String adminFee;
  private String gstAmount;
  private String vehicleId;
  private String jobNo;
  private String driverId;
  private String companyCode;

  // System update
  private String entryMode;
  private String nii;
  private String terminalId;
  private String merchantId;
  private String offlineIndicator = "N";
  private Instant requestTimeStamp = Instant.now();
  private Timestamp completionTimeStamp;

  private String txnStatus;

  @CreationTimestamp private Instant createdAt;
  @UpdateTimestamp private Instant updatedAt;

  // Result from AS
  private String processCode; // bit 3
  private String sysTraceNo; // bit 11
  private String retRefNo; // bit 37
  private String authorizationCode; // bit 38
  private String responseCode; // bit 39

  // From ISO message
  private String cardNumber;
  private String expiryDate;
  private String track2Data;
  private String posCondMode;

  @Column(length = 999)
  private String tripInfo;
}
