package com.cdg.zigpro.reconciliation.domain.models;

import lombok.Data;

@Data
public class MerchantItemDetailRecord {
  private String recordType;
  private String jobNo;
  private String vehicleNo;
  private String driverIC;
  private String transactionDateTime;
  private String paymentMode;
  private String cardNo;
  private String approvalCode;
  private String fareAmount;
  private String adminAmount;
  private String gstAmount;
  private String amountOfTrans;
  private String terminalID;
  private String merchantID;
  private String systemTraceNo;
  private String companyCode;
  private String tripStart;
  private String tripEnd;
  private String pickupAddress;
  private String destination;
  private String salesDraftNumber;
  private String cardType;
  private String transactionSettlementDate;
  private String bankTID;
  private String bankBatchCloseNo;
  private String offlineTransactionDateTime;
  private String offlineIndicator;
  private String lengthOfHotelCardInfo;
  private String hotelCardInformation;
  private String filler;
}
