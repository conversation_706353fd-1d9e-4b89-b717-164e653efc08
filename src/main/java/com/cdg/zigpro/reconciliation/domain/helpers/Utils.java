package com.cdg.zigpro.reconciliation.domain.helpers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Random;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Utils {
  // private static final int RANDOM_STRING_LENGTH = 10;
  private static final int RANDOM_AMOUNT_SCALE = 2;
  private static final int RANDOM_AMOUNT_BOUND = 1000000;
  private static final String ALPHANUMERIC_CHARS =
      "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
  private static final String NUMERIC_CHARS = "0123456789";
  private static final Random random = new SecureRandom();

  public static String generateRandomAlphanumeric(int length) {
    // Random random = new SecureRandom();
    StringBuilder sb = new StringBuilder(length);

    for (int i = 0; i < length; i++) {
      int randomIndex = random.nextInt(ALPHANUMERIC_CHARS.length());
      char randomChar = ALPHANUMERIC_CHARS.charAt(randomIndex);
      sb.append(randomChar);
    }

    return sb.toString();
  }

  public static String generateRandomNumeric(int length) {
    StringBuilder sb = new StringBuilder(length);

    for (int i = 0; i < length; i++) {
      int randomIndex = random.nextInt(NUMERIC_CHARS.length());
      char randomChar = NUMERIC_CHARS.charAt(randomIndex);
      sb.append(randomChar);
    }

    return sb.toString();
  }

  public static String generateSaleData(Integer salesCount, BigDecimal salesAmount) {
    String salesTotalCount = StringUtil.appendLeft("" + salesCount, 3, "0");
    log.info("salesTotalCount:" + salesTotalCount);
    String salesTotalAmount = StringUtil.appendLeft("" + salesAmount, 12, "0");
    log.info("salesTotalAmount:" + salesTotalAmount);

    String refundCount = StringUtil.appendLeft("", 3, "0");
    log.info("refundCount:" + refundCount);
    String refundAmount = StringUtil.appendLeft("", 12, "0");
    log.info("refundAmount:" + refundAmount);

    String debitSalesCount = StringUtil.appendLeft("", 3, "0");
    log.info("debitSalesCount:" + debitSalesCount);
    String debitSalesAmount = StringUtil.appendLeft("", 12, "0");
    log.info("debitSalesAmount:" + debitSalesAmount);

    String debitRefundCount = StringUtil.appendLeft("", 3, "0");
    log.info("debitRefundCount:" + debitRefundCount);
    String debitRefundAmount = StringUtil.appendLeft("", 12, "0");
    log.info("debitRefundAmount:" + debitRefundAmount);

    String preAuthCount = StringUtil.appendLeft("", 3, "0");
    log.info("preAuthCount:" + preAuthCount);
    String preAuthAmount = StringUtil.appendLeft("", 12, "0");
    log.info("preAuthAmount:" + preAuthAmount);

    String preAuthRefundCount = StringUtil.appendLeft("", 3, "0");
    log.info("preAuthRefundCount:" + preAuthRefundCount);
    String preAuthRefundAmount = StringUtil.appendLeft("", 12, "0");
    log.info("preAuthRefundAmount:" + preAuthRefundAmount);
    StringBuilder sb = new StringBuilder(salesTotalCount);
    sb.append(salesTotalAmount)
        .append(refundCount)
        .append(refundAmount)
        .append(debitSalesCount)
        .append(debitSalesAmount)
        .append(debitRefundCount)
        .append(debitRefundAmount)
        .append(preAuthCount)
        .append(preAuthAmount)
        .append(preAuthRefundCount)
        .append(preAuthRefundAmount);

    return sb.toString();
  }

  public static String getCurrentDateInYYYMMDD() {
    LocalDate currentDate = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    return currentDate.format(formatter);
  }

  public static String getCurrentTimeInHHMM() {
    LocalTime currentTime = LocalTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HHmm");
    return currentTime.format(formatter);
  }

  public static String convertToTimestampString(Timestamp timestamp) {
    if (timestamp == null) return "";
    LocalDateTime localDateTime = timestamp.toLocalDateTime();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    String formattedDateTime = localDateTime.format(formatter);
    return formattedDateTime;
  }

  public static String convertToSGTimestampString(Timestamp timestamp) {
    if (timestamp == null) return "";
    LocalDateTime localDateTime = timestamp.toLocalDateTime();
    ZonedDateTime zonedDateTime =
        localDateTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(ZoneId.of("Asia/Singapore"));
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    String formattedDateTime = zonedDateTime.format(formatter);
    return formattedDateTime;
  }

  public static String convertAmountToIsoString(BigDecimal amount) {
    amount = amount == null ? new BigDecimal(0) : amount;
    var isoAmount = amount.multiply(new BigDecimal(100));
    BigDecimal roundedNumber = isoAmount.setScale(0, RoundingMode.HALF_UP);
    return roundedNumber.toString();
  }
}
