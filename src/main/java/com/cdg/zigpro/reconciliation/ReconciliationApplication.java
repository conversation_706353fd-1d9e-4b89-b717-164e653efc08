package com.cdg.zigpro.reconciliation;

import java.util.TimeZone;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableAsync
public class ReconciliationApplication {

  public static void main(String[] args) {
    // Set default timezone to UTC+8
    TimeZone.setDefault(TimeZone.getTimeZone("Asia/Singapore"));
    SpringApplication.run(ReconciliationApplication.class, args);
  }
}
