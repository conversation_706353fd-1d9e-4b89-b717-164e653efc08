package com.cdg.zigpro.reconciliation.infrastructure.configuration;

import java.io.IOException;
import java.io.InputStream;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.channel.PostChannel;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

@Configuration
@Slf4j
public class AuthorizationServerChannelFactory {
  @Value("${as.server.ip}")
  private String SERVER_IP;

  @Value("${as.server.port}")
  private int SERVER_PORT;

  @Value("${as.iso.file}")
  private String PACKAGER_FILE = "iso87binary-CREDIT.xml";

  // Use a single volatile channel instance. Volatile ensures visibility across threads.
  private volatile PostChannel channel;

  /**
   * Initializes the PostChannel using Spring's @PostConstruct lifecycle hook. This ensures the
   * channel is connected and ready before the application starts using it.
   */
  @PostConstruct
  public void init() throws ISOException, IOException {
    // Use try-with-resources to ensure the InputStream is always closed, preventing resource leaks.
    try (InputStream packagerFileStream = new ClassPathResource(PACKAGER_FILE).getInputStream()) {
      MyPackager packager = new MyPackager(packagerFileStream);
      PostChannel newChannel = new PostChannel(SERVER_IP, SERVER_PORT, packager);
      newChannel.setHeader("6000120000"); // Set the header bytes
      log.info("Connecting to Authorization Server at {}:{}", SERVER_IP, SERVER_PORT);
      newChannel.connect();
      this.channel = newChannel; // Safely publish the connected channel.
    }
  }

  /**
   * Sends an ISO8583 message and waits for a response. This method is synchronized to ensure that
   * only one thread can perform the send/receive operation at a time, preventing data stream
   * corruption.
   *
   * <p><b>Note:</b> This synchronization creates a performance bottleneck, as it serializes all
   * requests to the authorization server. For higher throughput, a pool of channels would be
   * required.
   */
  public synchronized ISOMsg sendIsoMsg(ISOMsg isoMsg) throws ISOException, IOException {
    if (channel == null || !channel.isConnected()) {
      log.error("Channel is not connected. Attempting to reconnect...");
      reconnect(); // Attempt to recover the connection
    }
    log.debug("Sending ISO message: {}", isoMsg);
    channel.send(isoMsg);
    ISOMsg response = channel.receive();
    log.debug("Received ISO response: {}", response);
    return response;
  }

  /**
   * Disconnects the channel when the application is shutting down. Managed by Spring's @PreDestroy
   * lifecycle hook.
   */
  @PreDestroy
  public void disconnect() {
    if (this.channel != null && this.channel.isConnected()) {
      log.info("Disconnecting from Authorization Server.");
      try {
        this.channel.disconnect();
      } catch (IOException e) {
        log.error("Error while disconnecting channel.", e);
      }
    }
  }

  private synchronized void reconnect() throws IOException {
    if (channel != null) {
      channel.reconnect();
    }
  }

  private class MyPackager extends org.jpos.iso.packager.GenericPackager {
    public MyPackager(InputStream packagerFileStream) throws ISOException {
      super(packagerFileStream);
    }
  }
}
