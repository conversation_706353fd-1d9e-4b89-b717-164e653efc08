package com.cdg.zigpro.reconciliation.infrastructure;

import com.cdg.zigpro.reconciliation.domain.constants.IsoConstants;
import com.cdg.zigpro.reconciliation.domain.helpers.Utils;
import com.cdg.zigpro.reconciliation.domain.models.BatchUploadRequest;
import com.cdg.zigpro.reconciliation.domain.models.BatchUploadResponse;
import com.cdg.zigpro.reconciliation.domain.models.SettlementRequest;
import com.cdg.zigpro.reconciliation.domain.models.SettlementResponse;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTrailerRequest;
import com.cdg.zigpro.reconciliation.domain.models.SettlementTrailerResponse;
import com.cdg.zigpro.reconciliation.domain.services.IAuthorizationServerService;
import com.cdg.zigpro.reconciliation.domain.utils.MaskingUtil;
import com.cdg.zigpro.reconciliation.infrastructure.configuration.AuthorizationServerChannelFactory;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;
import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class IsoAuthorizationServerService implements IAuthorizationServerService {

  private AuthorizationServerChannelFactory channelFactory;

  public IsoAuthorizationServerService(AuthorizationServerChannelFactory channelFactory) {
    this.channelFactory = channelFactory;
  }

  @Override
  public SettlementResponse processSettlement(SettlementRequest request) {
    // var channel = channelFactory.getChannel();
    // Create ISO Message
    ISOMsg isoMsg = new ISOMsg();
    try {
      isoMsg.setMTI(IsoConstants.MTID_SETTLEMENT_REQ);
    } catch (ISOException e) {
      log.error("error while setting the MTI:");
      e.printStackTrace();
    }

    isoMsg.set(3, IsoConstants.PC_SETTLEMENT);
    isoMsg.set(11, request.getTraceNo());

    isoMsg.set(24, IsoConstants.NETWORK_INTERNATIONAL_IDENTIFIER_);
    isoMsg.set(25, IsoConstants.POS_CONDITION_MODE_NORMAL);

    isoMsg.set(41, request.getTid());
    isoMsg.set(42, request.getMid());
    isoMsg.set(60, request.getBatchNo());
    var salesTotalAmount = request.getSalesTotalAmount().multiply(new BigDecimal(100));
    BigDecimal roundedNumber = salesTotalAmount.setScale(0, RoundingMode.HALF_UP);

    isoMsg.set(63, Utils.generateSaleData(request.getSalesTotalCount(), roundedNumber));

    for (int i = 1; i <= 128; i++) {
      if (isoMsg.hasField(i) && (isoMsg.getString(i) == null || isoMsg.getString(i).isEmpty())) {
        isoMsg.unset(i);
      }
    }

    log.info(
        "req: 3: {} 11: {} 24: {} 25: {} 41: {} 42: {} 60: {} 63: {}",
        isoMsg.getString(3),
        isoMsg.getString(11),
        isoMsg.getString(24),
        isoMsg.getString(25),
        isoMsg.getString(41),
        isoMsg.getString(42),
        isoMsg.getString(60),
        isoMsg.getString(63));
    var settlementRes = new SettlementResponse();
    try {
      // channel.send(isoMsg);
      // ISOMsg response = channel.receive();
      log.info(
          "processSettlement Trace No {} Sending AS Settelment message:", request.getTraceNo());
      printIso(isoMsg);

      ISOMsg response = channelFactory.sendIsoMsg(isoMsg);
      log.info(
          "processSettlement Trace No {} Received AS Settlement response:", request.getTraceNo());
      printIso(response);

      settlementRes.setAuthorizationCode(response.getString(38));
      settlementRes.setResponseCode(response.getString(39));
      settlementRes.setRetrievalRefNo(response.getString(37));
      settlementRes.setLocalTransTime(response.getString(12));
      settlementRes.setLocalTransDate(response.getString(13));
      settlementRes.setTraceNo(response.getString(11));
      settlementRes.setTid(response.getString(41));

    } catch (IOException e) {
      log.error("IO error while sending and receiving iso message:");
      e.printStackTrace();
      settlementRes.setResponseCode("96");
    } catch (ISOException e) {
      log.error("ISO error while sending and receiving iso message:");
      e.printStackTrace();
      settlementRes.setResponseCode("96");
    }

    log.info(
        "processSettlement Trace No {} Settlement response:{}",
        request.getTraceNo(),
        settlementRes);

    return settlementRes;
  }

  @Override
  public BatchUploadResponse uploadBatch(BatchUploadRequest request) {
    // var channel = channelFactory.getChannel();
    // Create ISO Message
    ISOMsg isoMsg = new ISOMsg();
    try {
      isoMsg.setMTI(IsoConstants.MTID_BATCH_UPLOAD_REQ);
    } catch (ISOException e) {
      log.error("error while setting the MTI:");
      e.printStackTrace();
    }

    isoMsg.set(2, request.getCardNo());
    isoMsg.set(3, IsoConstants.PC_SETTLEMENT);
    var amount = request.getAmount().multiply(new BigDecimal(100));
    BigDecimal roundedNumber = amount.setScale(0, RoundingMode.HALF_UP);
    isoMsg.set(4, roundedNumber.toString());

    isoMsg.set(11, request.getTraceNo());
    isoMsg.set(14, request.getExpiryDate());

    isoMsg.set(24, IsoConstants.NETWORK_INTERNATIONAL_IDENTIFIER_);
    isoMsg.set(25, request.getPosConditionalMode());
    isoMsg.set(37, request.getRetrievalRefNo());
    isoMsg.set(39, request.getResponseCode());

    isoMsg.set(41, request.getTid());
    isoMsg.set(42, request.getMid());
    isoMsg.set(60, "0200" + request.getTraceNo());

    for (int i = 1; i <= 128; i++) {
      if (isoMsg.hasField(i) && (isoMsg.getString(i) == null || isoMsg.getString(i).isEmpty())) {
        isoMsg.unset(i);
      }
    }

    log.info(
        "req: 2: {} 3: {} 4: {} 11: {} 24: {} 25: {} 37: {} 39: {} 41: {} 42: {} 60: {}",
        MaskingUtil.maskPan(isoMsg.getString(2)),
        isoMsg.getString(3),
        isoMsg.getString(4),
        isoMsg.getString(11),
        isoMsg.getString(24),
        isoMsg.getString(25),
        isoMsg.getString(37),
        isoMsg.getString(39),
        isoMsg.getString(41),
        isoMsg.getString(42),
        isoMsg.getString(60));

    var batchUploadRes = new BatchUploadResponse();
    try {
      // channel.send(isoMsg);
      // ISOMsg response = channel.receive();
      ISOMsg response = channelFactory.sendIsoMsg(isoMsg);
      printIso(response);

      batchUploadRes.setAuthorizationCode(response.getString(38));
      batchUploadRes.setResponseCode(response.getString(39));
      batchUploadRes.setRetrievalRefNo(response.getString(37));
      batchUploadRes.setLocalTransTime(response.getString(12));
      batchUploadRes.setLocalTransDate(response.getString(13));
      batchUploadRes.setTraceNo(response.getString(11));
      batchUploadRes.setTid(response.getString(41));

    } catch (IOException e) {
      log.error("IO error while sending and receiving iso message:");
      e.printStackTrace();
      batchUploadRes.setResponseCode("96");
    } catch (ISOException e) {
      log.error("ISO error while sending and receiving iso message:");
      e.printStackTrace();
      batchUploadRes.setResponseCode("96");
    }
    return batchUploadRes;
  }

  @Override
  public SettlementTrailerResponse processSettlementTrailer(SettlementTrailerRequest request) {
    // var channel = channelFactory.getChannel();
    // Create ISO Message
    ISOMsg isoMsg = new ISOMsg();
    try {
      isoMsg.setMTI(IsoConstants.MTID_SETTLEMENT_REQ);
    } catch (ISOException e) {
      log.error("error while setting the MTI:");
      e.printStackTrace();
    }

    isoMsg.set(3, IsoConstants.PC_SETTLEMENT_TRAILER);
    isoMsg.set(11, request.getTraceNo());

    isoMsg.set(24, IsoConstants.NETWORK_INTERNATIONAL_IDENTIFIER_SETTLMENT_TRAILER);
    isoMsg.set(25, IsoConstants.POS_CONDITION_MODE_NORMAL);

    isoMsg.set(41, request.getTid());
    isoMsg.set(42, request.getMid());
    isoMsg.set(60, request.getBatchNo());

    var salesTotalAmount = request.getSalesTotalAmount().multiply(new BigDecimal(100));
    BigDecimal roundedNumber = salesTotalAmount.setScale(0, RoundingMode.HALF_UP);
    isoMsg.set(63, Utils.generateSaleData(request.getSalesTotalCount(), roundedNumber));

    for (int i = 1; i <= 128; i++) {
      if (isoMsg.hasField(i) && (isoMsg.getString(i) == null || isoMsg.getString(i).isEmpty())) {
        isoMsg.unset(i);
      }
    }

    log.info(
        "req: 3: {} 11: {} 24: {} 25: {} 41: {} 42: {} 60: {} 63: {}",
        isoMsg.getString(3),
        isoMsg.getString(11),
        isoMsg.getString(24),
        isoMsg.getString(25),
        isoMsg.getString(41),
        isoMsg.getString(42),
        isoMsg.getString(60),
        isoMsg.getString(63));
    var settlementTrailerRes = new SettlementTrailerResponse();
    try {
      // channel.send(isoMsg);
      // ISOMsg response = channel.receive();
      ISOMsg response = channelFactory.sendIsoMsg(isoMsg);
      printIso(response);

      settlementTrailerRes.setAuthorizationCode(response.getString(38));
      settlementTrailerRes.setResponseCode(response.getString(39));
      settlementTrailerRes.setRetrievalRefNo(response.getString(37));
      settlementTrailerRes.setLocalTransTime(response.getString(12));
      settlementTrailerRes.setLocalTransDate(response.getString(13));
      settlementTrailerRes.setTraceNo(response.getString(11));
      settlementTrailerRes.setTid(response.getString(41));

    } catch (IOException e) {
      log.error("IO error while sending and receiving iso message:");
      e.printStackTrace();
      settlementTrailerRes.setResponseCode("96");
    } catch (ISOException e) {
      log.error("ISO error while sending and receiving iso message:");
      e.printStackTrace();
      settlementTrailerRes.setResponseCode("96");
    }
    return settlementTrailerRes;
  }

  private void printIso(ISOMsg msg) {
    try {
      log.info("MTI: " + msg.getMTI());
    } catch (ISOException e) {
      e.printStackTrace();
    }

    for (int i = 1; i <= msg.getMaxField(); i++) {
      if (msg.hasField(i)) {
        String value = msg.getString(i);
        if (value != null && !value.isEmpty()) {
          log.info("Field " + i + ": " + value);
        }
      }
    }
  }

  @Override
  public void disconnect() {
    log.info("disconnecting from Authorization Server...");
    channelFactory.disconnectChannels();
  }

  @Override
  public void initChannels() throws ISOException, IOException {
    log.info("initializing Authorization Server channels...");
    channelFactory.init();
    log.info("Authorization Server channels initialized");
  }

  @Override
  public void reconnectChannels() {
    log.info("re-initializing Authorization Server channels...");
    channelFactory.reconnectChannels();
    log.info("Authorization Server channels re-initialized");
  }
}
