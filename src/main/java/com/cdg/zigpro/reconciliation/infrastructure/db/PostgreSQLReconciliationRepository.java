package com.cdg.zigpro.reconciliation.infrastructure.db;

import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchTransaction;
import com.cdg.zigpro.reconciliation.domain.services.IReconciliationRepository;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public class PostgreSQLReconciliationRepository implements IReconciliationRepository {
  private final ReconciliationMapper reconciliationMapper;

  public PostgreSQLReconciliationRepository(ReconciliationMapper reconciliationMapper) {
    this.reconciliationMapper = reconciliationMapper;
  }

  public void insertBatchProcess(BatchProcess batchProcess) {
    reconciliationMapper.insertBatchProcess(batchProcess);
  }

  public Long getBatchProcessSequence() {
    return reconciliationMapper.getBatchProcessSequence();
  }

  public void insertBatchTransactions(List<BatchTransaction> batchTransactions) {
    reconciliationMapper.insertBatchTransactions(batchTransactions);
  }

  @Override
  public void updateBatchReconFileName(BatchProcess batchProcess) {
    reconciliationMapper.updateBatchReconFileName(batchProcess);
  }

  @Override
  public void updateBatchProcess(BatchProcess batchProcess) {
    reconciliationMapper.updateBatchProcess(batchProcess);
  }

  @Override
  public BatchProcess getBatchProcessByBatchNo(String batchNo) {
    return reconciliationMapper.getBatchProcessByBatchNo(batchNo);
  }
}
