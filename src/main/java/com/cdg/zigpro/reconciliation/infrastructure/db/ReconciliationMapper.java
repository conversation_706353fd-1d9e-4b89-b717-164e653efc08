package com.cdg.zigpro.reconciliation.infrastructure.db;

import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchTransaction;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface ReconciliationMapper {
  @Insert(
      "INSERT INTO batch_process (batch_no, status, created_at, updated_at, created_by) "
          + "VALUES (#{batchProcess.batchNo}, #{batchProcess.status}, #{batchProcess.createdAt}, "
          + "#{batchProcess.updatedAt}, #{batchProcess.createdBy})")
  void insertBatchProcess(@Param("batchProcess") BatchProcess batchProcess);

  @Select("SELECT nextval('batch_process_sequence')")
  Long getBatchProcessSequence();

  @Insert(
      "<script>"
          + "INSERT INTO batch_transaction (transaction_id, batch_no, merchant_id, terminal_id, status, created_at, updated_at, created_by) "
          + "VALUES "
          + "<foreach collection='batchTransactions' item='batchTransaction' separator=','>"
          + "(#{batchTransaction.transactionId}, #{batchTransaction.batchNo}, #{batchTransaction.merchantId}, #{batchTransaction.terminalId}, #{batchTransaction.status}, "
          + "#{batchTransaction.createdAt}, #{batchTransaction.updatedAt}, #{batchTransaction.createdBy})"
          + "</foreach>"
          + "</script>")
  void insertBatchTransactions(
      @Param("batchTransactions") List<BatchTransaction> batchTransactions);

  @Update(
      "UPDATE batch_process "
          + " SET status = #{batchProcess.status}"
          + ", terminal_id = #{batchProcess.terminalId}"
          + ", merchant_id = #{batchProcess.merchantId}"
          + ", sales_total_count = #{batchProcess.salesTotalCount}"
          + ", sales_total_amount = #{batchProcess.salesTotalAmount}"
          + ", authorization_code = #{batchProcess.authorizationCode}"
          + ", retrieval_ref_no = #{batchProcess.retrievalRefNo}"
          + ", response_code = #{batchProcess.responseCode}"
          + ", trace_no = #{batchProcess.traceNo}"
          + ", updated_at = #{batchProcess.updatedAt}"
          + ", updated_by = #{batchProcess.updatedBy}"
          + " WHERE batch_no = #{batchProcess.batchNo}")
  void updateBatchProcess(@Param("batchProcess") BatchProcess batchProcess);

  @Select("SELECT * FROM batch_process WHERE batch_no = #{batchNo}")
  BatchProcess getBatchProcessByBatchNo(@Param("batchNo") String batchNo);

  @Update(
      "UPDATE batch_process "
          + " SET settlement_file = #{batchProcess.settlementFile}"
          + " WHERE batch_no = #{batchProcess.batchNo}")
  void updateBatchReconFileName(@Param("batchProcess") BatchProcess batchProcess);
}
