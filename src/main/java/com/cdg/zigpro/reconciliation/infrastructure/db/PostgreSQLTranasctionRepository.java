package com.cdg.zigpro.reconciliation.infrastructure.db;

import com.cdg.zigpro.reconciliation.domain.models.entities.TransactionDetail;
import com.cdg.zigpro.reconciliation.domain.services.ITransactionRepository;
import java.util.List;
import org.springframework.stereotype.Repository;

@Repository
public class PostgreSQLTranasctionRepository implements ITransactionRepository {
  private final TransactionDetailMapper transactionDetailMapper;

  public PostgreSQLTranasctionRepository(TransactionDetailMapper transactionDetailMapper) {
    this.transactionDetailMapper = transactionDetailMapper;
  }

  @Override
  public List<TransactionDetail> getAllSuccessfulTransactions() {
    return transactionDetailMapper.getTransactionsByBatchStatus("CAPTURED");
  }

  @Override
  public List<TransactionDetail> getAllSuccessfulTransactions(String startDate, String endDate) {
    return transactionDetailMapper.getTransactionsByBatchStatusAndReconDate(
        "CAPTURED", startDate, endDate);
  }
}
