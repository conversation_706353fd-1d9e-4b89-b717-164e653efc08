package com.cdg.zigpro.reconciliation.infrastructure.db;

import com.cdg.zigpro.reconciliation.domain.models.entities.TransactionDetail;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface TransactionDetailMapper {

  List<TransactionDetail> getAllTransactionDetails();

  TransactionDetail getTransactionDetailById(@Param("transactionId") String transactionId);

  @Select(
      "SELECT * FROM transaction_detail WHERE (transaction_type = 'SALES' OR transaction_type = 'OFFLINE_SALES') AND response_code = '00'")
  List<TransactionDetail> getAllSuccessfulTransactions();

  @Select(
      "SELECT td.* "
          + "  FROM payment_transaction td "
          + "  LEFT JOIN batch_transaction bt "
          + "         ON td.id = bt.transaction_id "
          + "        AND bt.status = #{batchStatus} "
          + " WHERE bt.transaction_id IS NULL "
          + "  AND (td.transaction_type = 'PURCHASE' OR td.transaction_type = 'OFFLINE_PURCHASE') "
          + "  AND td.txn_status = 'A' "
          + "  AND td.response_code = '00' ")
  List<TransactionDetail> getTransactionsByBatchStatus(@Param("batchStatus") String batchStatus);

  @Select(
      "SELECT td.* "
          + "  FROM payment_transaction td "
          + "  LEFT JOIN batch_transaction bt "
          + "         ON td.id = bt.transaction_id "
          + "        AND bt.status = #{batchStatus} "
          + " WHERE bt.transaction_id IS NULL "
          + "  AND (td.transaction_type = 'PURCHASE' OR td.transaction_type = 'OFFLINE_PURCHASE') "
          + "  AND td.txn_status = 'A' "
          + "  AND td.response_code = '00' "
          + "  AND td.created_at >=TO_TIMESTAMP(#{startDate}, 'YYYYMMDD HH24:MI:SS') "
          + "  AND td.created_at <=TO_TIMESTAMP(#{endDate}, 'YYYYMMDD HH24:MI:SS') ")
  List<TransactionDetail> getTransactionsByBatchStatusAndReconDate(
      @Param("batchStatus") String batchStatus,
      @Param("startDate") String startDate,
      @Param("endDate") String endDate);
}
