package com.cdg.zigpro.reconciliation.application;

import com.cdg.zigpro.reconciliation.domain.models.ReconciliationJobParam;
import com.cdg.zigpro.reconciliation.domain.models.entities.BatchProcess;
import com.cdg.zigpro.reconciliation.domain.services.PaymentPortalService;
import java.nio.file.Path;
import java.nio.file.Paths;
import io.micrometer.common.util.StringUtils;
import java.io.File;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequiredArgsConstructor
@Slf4j
public class PaymentPortalController {

  private final PaymentPortalService paymentPortalService;

  @Value("${settlement.output.file.path}")
  private String SETTLEMENT_FILE_PATH;

  @PostMapping("/v1.0/settlements/execute")
  public ResponseEntity<Void> executeSettlementProcess(@RequestBody ReconciliationJobParam jobParam)
      throws InterruptedException {
    paymentPortalService.executeSettlementProcess(jobParam);
    return ResponseEntity.ok(null);
  }

  @GetMapping("/v1.0/settlements/download/{batchNo}")
  public ResponseEntity<Resource> getCabchargeSettlementFile(
      @PathVariable(name = "batchNo") String batchNo) {
    BatchProcess batchProcess = paymentPortalService.getBatchProcessByBatchNo(batchNo);
    String filename = batchProcess.getSettlementFile();
    if (StringUtils.isBlank(filename)) {
      return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
    }

    // Securely resolve the file path to prevent path traversal attacks.
    Path settlementDir = Paths.get(SETTLEMENT_FILE_PATH);
    Path requestedPath = settlementDir.resolve(filename).normalize();

    // Ensure the resolved path is still within the intended directory.
    if (!requestedPath.startsWith(settlementDir)) {
      log.warn("Path traversal attempt detected for filename: {}", filename);
      return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(null);
    }

    File file = requestedPath.toFile();

    if (!file.exists()) {
      return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
    }

    // Create a FileSystemResource
    Resource resource = new FileSystemResource(file);

    // Set the content-disposition header to prompt a download
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\"");

    // Return the resource and headers
    return ResponseEntity.ok().headers(headers).contentLength(file.length()).body(resource);
  }
}
