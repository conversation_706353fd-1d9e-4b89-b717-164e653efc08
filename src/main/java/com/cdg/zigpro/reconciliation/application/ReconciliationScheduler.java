package com.cdg.zigpro.reconciliation.application;

import com.cdg.zigpro.reconciliation.application.config.SchedulerConfig;
import com.cdg.zigpro.reconciliation.domain.services.IReconciliationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Deprecated
@Component
@EnableScheduling
@Slf4j
public class ReconciliationScheduler {
  private final IReconciliationService reconciliationService;
  private final SchedulerConfig schedulerConfig;

  public ReconciliationScheduler(
      IReconciliationService reconciliationService, SchedulerConfig schedulerConfig) {
    this.reconciliationService = reconciliationService;
    this.schedulerConfig = schedulerConfig;
  }

  @Deprecated //move to aws eventbridge
//  @Scheduled(cron = "#{schedulerConfig.cron}", zone = "#{schedulerConfig.timezone}")
  public void runReconciliationProcess() throws InterruptedException {
    reconciliationService.process(null);
  }
}
