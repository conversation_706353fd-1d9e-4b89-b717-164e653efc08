apiVersion: v1
kind: PersistentVolume
metadata:
  name: pv-obs-cabcharge-sftp-ftpjobs
  annotations:
    pv.kubernetes.io/bound-by-controller: 'yes'
    pv.kubernetes.io/provisioned-by: everest-csi-provisioner
spec:
  accessModes:
    - ReadWriteMany
  capacity:
    storage: 10Gi
  csi:
    driver: obs.csi.everest.io
    volumeHandle: secure-nonprod
    fsType: obsfs
    volumeAttributes:
      storage.kubernetes.io/csiProvisionerIdentity: everest-csi-provisioner
      everest.io/enterprise-project-id: '0'
      everest.io/obs-bucket-redundancy: FUSION
      everest.io/obs-volume-type: STANDARD
      everest.io/region: ap-southeast-3
    nodePublishSecretRef:
      name: payment-sftp-secret
      namespace: sit
  persistentVolumeReclaimPolicy: Retain
  storageClassName: csi-obs
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: pvc-obs-cabcharge-sftp-ftpjobs
  namespace: sit
  annotations:
    csi.storage.k8s.io/fstype: obsfs
    csi.storage.k8s.io/node-publish-secret-name: payment-sftp-secret
    csi.storage.k8s.io/node-publish-secret-namespace: sit
    everest.io/enterprise-project-id: '0'
    everest.io/obs-bucket-redundancy: FUSION
    everest.io/obs-volume-type: STANDARD
    pv.kubernetes.io/bind-completed: 'yes'
    volume.beta.kubernetes.io/storage-provisioner: everest-csi-provisioner
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi
  storageClassName: csi-obs
  volumeName: pv-obs-cabcharge-sftp-ftpjobs
---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: SERVICE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: SERVICE
  template:
    metadata:
      labels:
        app: SERVICE
    spec:
      containers:
        - name: SERVICE-c
          image: IMAGE
          ports:
            - containerPort: 8080
          imagePullPolicy: Always
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            periodSeconds: 10
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            periodSeconds: 10
            successThreshold: 1
          lifecycle:
            preStop:
              exec:
                command:
                  - sh
                  - -c
                  - "sleep 5"          
          volumeMounts:
            - name: app-config
              mountPath: /app/application.yml
              subPath: application.yml
            - name: secure-nonprod-obs-volume
              mountPath: /data
      imagePullSecrets:
        - name: swr-secret
      volumes:
        - name: app-config
          configMap:
            name: SERVICE
        - name: secure-nonprod-obs-volume
          persistentVolumeClaim:
            claimName: pvc-obs-cabcharge-sftp-ftpjobs
---
apiVersion: v1
kind: Service
metadata:
  name: SERVICE
  labels:
    app: SERVICE

spec:
  selector:
    app: SERVICE
  ports:
    - name: http
      port: 38119
      targetPort: 8080
  type: ClusterIP
  sessionAffinity: None
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: SERVICE
data:
  application.yml: |
    spring:
      profiles:
        active: sit
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: SERVICE
spec:
  ingressClassName: kong-sit-external
  rules:
    - host: cabchargegatewayrecon.zigpro.apps.nonprod-cce-1-27.cce.internal.sit.sg.huawei.zig.systems
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: SERVICE
                port:
                  name: http
    - host: cabchargegatewayrecon.zigpro.apps.internal.sit.sg.huawei.zig.systems
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: SERVICE
                port:
                  name: http
    - host: cabchargegatewayrecon.zigpro.nprod.zig.systems
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: SERVICE
                port:
                  name: http
    - host: cabchargegatewayrecon-sit.zig.live
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: SERVICE
                port:
                  name: http
